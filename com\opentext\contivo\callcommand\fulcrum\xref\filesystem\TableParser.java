/*     */ package com.opentext.contivo.callcommand.fulcrum.xref.filesystem;
/*     */ 
/*     */ import fj.P;
/*     */ import fj.P2;
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TableParser
/*     */ {
/*     */   public static P2<Map<String, Integer>, List<Map<Integer, String>>> parse(File tableFile, boolean hasColHeaders) throws Exception {
/*  40 */     BufferedReader reader = null;
/*  41 */     Map<String, Integer> colNameToNumber = new HashMap<>();
/*     */     try {
/*  43 */       InputStream stream = new BufferedInputStream(new FileInputStream(tableFile));
/*  44 */       reader = new BufferedReader(new InputStreamReader(stream));
/*  45 */       List<Map<Integer, String>> rows = new ArrayList<>();
/*  46 */       rows.add(new HashMap<>());
/*  47 */       int next = -1;
/*  48 */       StringBuilder builder = new StringBuilder();
/*  49 */       boolean inQuotes = false;
/*  50 */       boolean expectDelimiterOrQuote = false;
/*  51 */       int currentColIndex = 1;
/*  52 */       boolean processingHeader = hasColHeaders;
/*     */ 
/*     */       
/*  55 */       while ((next = reader.read()) != -1) {
/*  56 */         char nextChar = (char)next;
/*     */         
/*  58 */         if (expectDelimiterOrQuote) {
/*  59 */           if (nextChar == '"') {
/*  60 */             builder.append(nextChar);
/*  61 */             inQuotes = true;
/*  62 */             expectDelimiterOrQuote = false; continue;
/*     */           } 
/*  64 */           if (nextChar != ',' && nextChar != '\r' && nextChar != '\n') {
/*  65 */             throw new Exception("IllegalCharacter " + nextChar + ". Expected ',' or delimiter.");
/*     */           }
/*  67 */           expectDelimiterOrQuote = false;
/*     */         } 
/*     */         
/*  70 */         switch (nextChar) {
/*     */           case '\r':
/*  72 */             if (inQuotes) {
/*  73 */               builder.append(nextChar);
/*     */             }
/*     */             continue;
/*     */           case '\n':
/*  77 */             if (!inQuotes) {
/*  78 */               if (processingHeader) {
/*  79 */                 colNameToNumber.put(builder.toString(), Integer.valueOf(currentColIndex));
/*  80 */                 processingHeader = false;
/*     */               } else {
/*  82 */                 ((Map<Integer, String>)rows.get(rows.size() - 1)).put(Integer.valueOf(currentColIndex), builder.toString());
/*  83 */                 rows.add(new HashMap<>());
/*     */               } 
/*  85 */               currentColIndex = 1;
/*  86 */               builder.setLength(0); continue;
/*     */             } 
/*  88 */             builder.append(nextChar);
/*     */             continue;
/*     */           
/*     */           case ',':
/*  92 */             if (!inQuotes) {
/*  93 */               if (processingHeader) {
/*  94 */                 colNameToNumber.put(builder.toString(), Integer.valueOf(currentColIndex));
/*     */               } else {
/*  96 */                 ((Map<Integer, String>)rows.get(rows.size() - 1)).put(Integer.valueOf(currentColIndex), builder.toString());
/*     */               } 
/*  98 */               currentColIndex++;
/*  99 */               builder.setLength(0); continue;
/*     */             } 
/* 101 */             builder.append(nextChar);
/*     */             continue;
/*     */           
/*     */           case '"':
/* 105 */             if (inQuotes) {
/* 106 */               inQuotes = false;
/* 107 */               if (builder.length() == 0) {
/* 108 */                 builder.append(nextChar); continue;
/*     */               } 
/* 110 */               expectDelimiterOrQuote = true;
/*     */               continue;
/*     */             } 
/* 113 */             inQuotes = true;
/*     */             continue;
/*     */         } 
/*     */         
/* 117 */         builder.append(nextChar);
/*     */       } 
/*     */ 
/*     */       
/* 121 */       if (builder.length() > 0) {
/* 122 */         ((Map<Integer, String>)rows.get(rows.size() - 1)).put(Integer.valueOf(currentColIndex), builder.toString());
/*     */       }
/* 124 */       return P.p(colNameToNumber, rows);
/* 125 */     } catch (Exception e) {
/* 126 */       throw new Exception("Unable to parse table: " + e.getMessage());
/*     */     } finally {
/*     */       try {
/* 129 */         if (reader != null) {
/* 130 */           reader.close();
/*     */         }
/* 132 */       } catch (IOException iOException) {}
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\filesystem\TableParser.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */