/*    */ package com.opentext.contivo.callcommand.fulcrum.xref.cache.table;
/*    */ 
/*    */ import com.google.common.cache.Cache;
/*    */ import com.google.common.cache.CacheBuilder;
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.Table;
/*    */ import java.util.concurrent.TimeUnit;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCTableGuavaCache
/*    */   implements FCTableCache
/*    */ {
/*    */   private static final int MAX_SIZE = 100;
/*    */   private final Cache<FCTableCacheKey, Table> cache;
/*    */   
/*    */   public FCTableGuavaCache(long expirationTime, long maxSize) {
/* 23 */     this
/*    */ 
/*    */       
/* 26 */       .cache = CacheBuilder.newBuilder().expireAfterAccess(expirationTime, TimeUnit.SECONDS).maximumSize(maxSize).build();
/*    */   }
/*    */   
/*    */   public FCTableGuavaCache() {
/* 30 */     this
/*    */ 
/*    */       
/* 33 */       .cache = CacheBuilder.newBuilder().expireAfterAccess(60L, TimeUnit.SECONDS).maximumSize(100L).build();
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Table get(FCTableCacheKey key) {
/* 39 */     return (Table)this.cache.getIfPresent(key);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void put(FCTableCacheKey key, Table value) {
/* 45 */     this.cache.put(key, value);
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\cache\table\FCTableGuavaCache.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */