package com.opentext.contivo.callcommand.fulcrum.xref.cache.value;

import java.util.Optional;

public interface FCValueCache {
  Optional<String> get(FCValueCacheKey paramFCValueCacheKey);
  
  void put(FCValueCacheKey paramFCValue<PERSON>ache<PERSON>ey, Optional<String> paramOptional);
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\cache\value\FCValueCache.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */