/*     */ package com.opentext.contivo.callcommand.fulcrum.enrichment.local;
/*     */ 
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.io.FileUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EnrichTableParser
/*     */ {
/*     */   public static Map<EnrichKey, String> parse(File tableFile) throws Exception {
/*  36 */     BufferedReader reader = null;
/*     */     try {
/*  38 */       tableFile.createNewFile();
/*  39 */       InputStream stream = new BufferedInputStream(new FileInputStream(tableFile));
/*  40 */       reader = new BufferedReader(new InputStreamReader(stream));
/*  41 */       Map<EnrichKey, String> rows = new HashMap<>();
/*  42 */       int next = -1;
/*  43 */       StringBuilder builder = new StringBuilder();
/*  44 */       boolean inQuotes = false;
/*  45 */       boolean expectDelimiterOrQuote = false;
/*  46 */       List<String> current = new ArrayList<>();
/*     */       
/*  48 */       while ((next = reader.read()) != -1) {
/*  49 */         char nextChar = (char)next;
/*     */         
/*  51 */         if (expectDelimiterOrQuote) {
/*  52 */           if (nextChar == '"') {
/*  53 */             builder.append(nextChar);
/*  54 */             inQuotes = true;
/*  55 */             expectDelimiterOrQuote = false; continue;
/*     */           } 
/*  57 */           if (nextChar != ',' && nextChar != '\r' && nextChar != '\n') {
/*  58 */             throw new Exception("IllegalCharacter " + nextChar + ". Expected ',' or delimiter.");
/*     */           }
/*  60 */           expectDelimiterOrQuote = false;
/*     */         } 
/*     */         
/*  63 */         switch (nextChar) {
/*     */           case '\r':
/*  65 */             if (inQuotes) {
/*  66 */               builder.append(nextChar);
/*     */             }
/*     */             continue;
/*     */           case '\n':
/*  70 */             if (!inQuotes) {
/*  71 */               current.add((builder.length() == 0) ? null : builder.toString());
/*  72 */               rows.put(new EnrichKey(current.get(0), current.get(1), current.get(2), current.get(3), current.get(4)), current.get(5));
/*  73 */               current.clear();
/*  74 */               builder.setLength(0); continue;
/*     */             } 
/*  76 */             builder.append(nextChar);
/*     */             continue;
/*     */           
/*     */           case ',':
/*  80 */             if (!inQuotes) {
/*  81 */               current.add((builder.length() == 0) ? null : builder.toString());
/*  82 */               builder.setLength(0); continue;
/*     */             } 
/*  84 */             builder.append(nextChar);
/*     */             continue;
/*     */           
/*     */           case '"':
/*  88 */             if (inQuotes) {
/*  89 */               inQuotes = false;
/*  90 */               if (builder.length() == 0) {
/*  91 */                 builder.append(nextChar); continue;
/*     */               } 
/*  93 */               expectDelimiterOrQuote = true;
/*     */               continue;
/*     */             } 
/*  96 */             inQuotes = true;
/*     */             continue;
/*     */         } 
/*     */         
/* 100 */         builder.append(nextChar);
/*     */       } 
/*     */ 
/*     */       
/* 104 */       if (builder.length() > 0) {
/* 105 */         current.add((builder.length() == 0) ? null : builder.toString());
/* 106 */         rows.put(new EnrichKey(current.get(0), current.get(1), current.get(2), current.get(3), current.get(4)), current.get(5));
/*     */       } 
/* 108 */       return rows;
/* 109 */     } catch (Exception e) {
/* 110 */       throw new Exception("Unable to parse table: " + e.getMessage());
/*     */     } finally {
/*     */       try {
/* 113 */         if (reader != null) {
/* 114 */           reader.close();
/*     */         }
/* 116 */       } catch (IOException iOException) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void persist(File tableFile, Map<EnrichKey, String> enrichMap) throws Exception {
/*     */     try {
/* 126 */       boolean first = true;
/* 127 */       for (Map.Entry<EnrichKey, String> entry : enrichMap.entrySet())
/*     */       {
/* 129 */         String row = ((EnrichKey)entry.getKey()).toString() + "," + ((EnrichKey)entry.getKey()).toString();
/* 130 */         if (first) {
/*     */           
/* 132 */           first = false;
/* 133 */           FileUtils.writeStringToFile(tableFile, row + "\n");
/*     */           
/*     */           continue;
/*     */         } 
/* 137 */         FileUtils.writeStringToFile(tableFile, row + "\n", true);
/*     */       }
/*     */     
/*     */     }
/* 141 */     catch (Exception e) {
/*     */       
/* 143 */       throw new Exception("Unable to save table: " + e.getMessage());
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\enrichment\local\EnrichTableParser.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */