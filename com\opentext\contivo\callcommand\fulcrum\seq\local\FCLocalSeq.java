/*    */ package com.opentext.contivo.callcommand.fulcrum.seq.local;
/*    */ 
/*    */ import com.opentext.contivo.callcommand.fulcrum.seq.FCSeqClient;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCLocalSeq
/*    */   implements FCSeqClient
/*    */ {
/* 18 */   private final Map<SeqKey, Long> seqMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public long getDbSequenceNumber(long increment, String mainKey, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/* 24 */     if (increment != 0L && increment != 1L) {
/* 25 */       throw new Exception("Increment value must be 0 or 1");
/*    */     }
/*    */     
/* 28 */     if (mainKey == null)
/*    */     {
/* 30 */       throw new Exception("First key must not be null");
/*    */     }
/*    */     
/* 33 */     SeqKey key = new SeqKey(mainKey, optionalKey1, optionalKey2, optionalKey3);
/* 34 */     Long seq = this.seqMap.get(key);
/* 35 */     if (seq == null) {
/*    */       
/* 37 */       this.seqMap.put(key, Long.valueOf(increment));
/*    */     }
/*    */     else {
/*    */       
/* 41 */       this.seqMap.put(key, Long.valueOf(seq.longValue() + increment));
/*    */     } 
/* 43 */     return ((Long)this.seqMap.get(key)).longValue();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setDbSequenceNumber(long seqNum, String mainKey, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/* 50 */     if (mainKey == null)
/*    */     {
/* 52 */       throw new Exception("First key must not be null");
/*    */     }
/* 54 */     SeqKey key = new SeqKey(mainKey, optionalKey1, optionalKey2, optionalKey3);
/* 55 */     this.seqMap.put(key, Long.valueOf(seqNum));
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\local\FCLocalSeq.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */