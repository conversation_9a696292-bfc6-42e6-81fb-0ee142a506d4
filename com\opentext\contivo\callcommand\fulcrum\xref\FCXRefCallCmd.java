/*     */ package com.opentext.contivo.callcommand.fulcrum.xref;
/*     */ 
/*     */ import com.contivo.analyst.util.ContivoExternalClass;
/*     */ import com.contivo.analyst.util.ContivoExternalMethod;
/*     */ import com.contivo.mixedruntime.RuntimeMessageException;
/*     */ import com.contivo.runtime.core.IContivoRuntime;
/*     */ import com.contivo.runtime.core.IContivoRuntimeSelfTimeout;
/*     */ import com.contivo.transform.MessageInfo;
/*     */ import com.contivo.transform.Tag;
/*     */ import com.opentext.contivo.callcommand.fulcrum.EnvProp;
/*     */ import com.opentext.contivo.callcommand.fulcrum.EnvironmentQuery;
/*     */ import com.opentext.contivo.callcommand.fulcrum.IPerformanceMetrics;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.DBPool;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.FCLookUp;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.SimpleConnectionToDBPool;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.filesystem.FileSystemLookUp;
/*     */ import java.io.File;
/*     */ import java.util.Arrays;
/*     */ import java.util.Optional;
/*     */ import java.util.Properties;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @ContivoExternalClass
/*     */ public class FCXRefCallCmd
/*     */   implements IContivoRuntimeSelfTimeout
/*     */ {
/*     */   public static final String DB_URL_PROP = "fc.db.url";
/*     */   public static final String DRIVER_NAME_PROP = "fc.db.driver";
/*     */   public static final String DB_USER_PROP = "fc.db.user";
/*     */   public static final String DB_PASSWORD_PROP = "fc.db.password";
/*     */   public static final String FS_PATH_PROP = "fc.fs.path";
/*     */   private FCXRefProp props;
/*     */   private LookUpClient client;
/*     */   private IPerformanceMetrics metrics;
/*     */   
/*     */   private void init(IContivoRuntime icr) throws RuntimeMessageException {
/*  67 */     if (this.client != null) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/*  72 */     MessageInfo info = new MessageInfo("FCXRefCallCmd.init", new Tag[] { Tag.FATAL });
/*     */     
/*  74 */     this.client = (LookUpClient)icr.getInstance(LookUpClient.class);
/*  75 */     this.props = (FCXRefProp)icr.getInstance(FCXRefProp.class);
/*  76 */     this.metrics = (IPerformanceMetrics)icr.getInstance(IPerformanceMetrics.class);
/*  77 */     Properties envJavaProp = EnvironmentQuery.getEnvJavaProp(icr);
/*  78 */     EnvProp envProp = EnvironmentQuery.getEnvProp(icr, envJavaProp);
/*     */ 
/*     */     
/*  81 */     Properties prop = icr.getUserProperties();
/*     */     
/*  83 */     if (this.props == null)
/*     */     {
/*  85 */       if (envProp != null) {
/*  86 */         this.props = new FCXRefProp(envProp.getSolutionID(), envProp.isTraceMode());
/*     */       } else {
/*  88 */         String solutionID = (String)Optional.<String>ofNullable(prop.getProperty("fc.solutionID")).orElseThrow(() -> new RuntimeMessageException(info, "Please specify the fc.solutionID in Tools->Options->User Properties Manager"));
/*     */         
/*  90 */         this.props = new FCXRefProp(solutionID);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*  95 */     if (this.client == null) {
/*  96 */       String fsPath = prop.getProperty("fc.fs.path");
/*  97 */       if (fsPath != null) {
/*  98 */         this.client = (LookUpClient)new FileSystemLookUp(new File(fsPath));
/*     */       } else {
/* 100 */         String dbURL = (String)Optional.<String>ofNullable(prop.getProperty("fc.db.url")).orElseThrow(() -> new RuntimeMessageException(info, "Please specify the fc.db.url in Tools->Options->User Properties Manager"));
/*     */         
/* 102 */         String driverName = (String)Optional.<String>ofNullable(prop.getProperty("fc.db.driver")).orElseThrow(() -> new RuntimeMessageException(info, "Please specify the fc.db.driver in Tools->Options->User Properties Manager"));
/*     */         
/* 104 */         String userName = prop.getProperty("fc.db.user");
/* 105 */         String password = prop.getProperty("fc.db.password");
/* 106 */         SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(dbURL, driverName, userName, password);
/* 107 */         this.client = (LookUpClient)new FCLookUp((DBPool)dbPool);
/*     */       } 
/*     */     } 
/*     */     
/* 111 */     if (this.client == null || this.props == null)
/*     */     {
/* 113 */       throw new RuntimeMessageException(info, "Failed to initialize");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "fcLookupValue")
/*     */   public String fcLookupValue(IContivoRuntime icr, String tableName, String findCol, String resultCol, String findValue) throws RuntimeMessageException {
/* 133 */     return fcLookupValue(icr, tableName, findCol, resultCol, findValue, "=");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "fcLookupValue")
/*     */   public String fcLookupValue(IContivoRuntime icr, String tableName, String findCol, String resultCol, String findValue, String op) throws RuntimeMessageException {
/* 140 */     return fcLookupValue(icr, tableName, new String[] { findCol }, resultCol, new String[] { findValue });
/*     */   }
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "fcLookupValue")
/*     */   public String fcLookupValue(IContivoRuntime icr, String tableName, String[] findCols, String resultCol, String[] findValues) throws RuntimeMessageException {
/* 146 */     String[] ops = new String[findCols.length];
/* 147 */     for (int i = 0; i < ops.length; i++) {
/* 148 */       ops[i] = "=";
/*     */     }
/* 150 */     return fcLookupValue(icr, tableName, findCols, resultCol, findValues, ops);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "fcLookupValue")
/*     */   public String fcLookupValue(IContivoRuntime icr, String tableName, String[] findCols, String resultCol, String[] findValues, String[] ops) throws RuntimeMessageException {
/* 157 */     return fcLookupValue(icr, tableName, findCols, resultCol, findValues, ops, false);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "fcLookupValueByColName")
/*     */   public String fcLookupValueByColName(IContivoRuntime icr, String tableName, String findCol, String resultCol, String findValue) throws RuntimeMessageException {
/* 164 */     return fcLookupValueByColName(icr, tableName, new String[] { findCol }, resultCol, new String[] { findValue });
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "fcLookupValueByColName")
/*     */   public String fcLookupValueByColName(IContivoRuntime icr, String tableName, String[] findCols, String resultCol, String[] findValues) throws RuntimeMessageException {
/* 171 */     String[] ops = new String[findCols.length];
/* 172 */     for (int i = 0; i < ops.length; i++) {
/* 173 */       ops[i] = "=";
/*     */     }
/* 175 */     return fcLookupValue(icr, tableName, findCols, resultCol, findValues, ops, true);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "fcLookupValueByColName")
/*     */   public String fcLookupValueByColName(IContivoRuntime icr, String tableName, String[] findCols, String resultCol, String[] findValues, String[] ops) throws RuntimeMessageException {
/* 182 */     return fcLookupValue(icr, tableName, findCols, resultCol, findValues, ops, true);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String fcLookupValue(IContivoRuntime icr, String tableName, String[] findCol, String resultCol, String[] findValue, String[] ops, boolean useColHeader) throws RuntimeMessageException {
/* 188 */     long startTime = System.currentTimeMillis();
/* 189 */     String prepend = useColHeader ? "FCXRefCallCmd.fcLookupValueByColName" : "FCXRefCallCmd.fcLookupValue";
/*     */     
/*     */     try {
/* 192 */       init(icr);
/* 193 */       if (findCol.length != findValue.length) {
/* 194 */         throw new Exception("The number of columns to be queried must match the number of query values. (" + findCol.length + ") columns were specified, but (" + findValue.length + ") query values were provided.");
/*     */       }
/*     */       
/* 197 */       if (findCol.length != ops.length) {
/* 198 */         throw new Exception("The number of columns to be queried must match the number of comparison operations. (" + findCol.length + ") columns were specified, but (" + findValue.length + ") operations were provided.");
/*     */       }
/*     */       
/* 201 */       Op[] _ops = new Op[findCol.length];
/* 202 */       for (int i = 0; i < ops.length; i++) {
/* 203 */         _ops[i] = Op.fromString(ops[i]);
/*     */       }
/* 205 */       String ret = this.client.lookupValue(this.props.getSolutionID(), tableName, findCol, resultCol, findValue, _ops, useColHeader);
/*     */       
/* 207 */       if (this.props.isTraceMode()) {
/* 208 */         icr.addTraceMessage(prepend, "{\"solutionId\":\"" + this.props
/* 209 */             .getSolutionID() + "\", \"table\":\"" + tableName + "\", \"findCol\":\"" + 
/* 210 */             Arrays.toString((Object[])findCol) + "\", \"resultCol\":\"" + resultCol + "\", \"findValue\":\"" + Arrays.toString((Object[])findValue) + "\", \"ops\"" + 
/* 211 */             Arrays.toString((Object[])ops) + "} = " + ret);
/*     */       }
/* 213 */       return (ret == null) ? "" : ret;
/* 214 */     } catch (RuntimeMessageException e) {
/* 215 */       throw e;
/* 216 */     } catch (Exception e) {
/* 217 */       MessageInfo info = new MessageInfo(prepend, new Tag[] { Tag.FATAL });
/* 218 */       throw new RuntimeMessageException(e, info, prepend + ": " + prepend);
/*     */     } finally {
/* 220 */       if (this.metrics != null) {
/* 221 */         this.metrics.incre("FCXRef.lookup", 1L);
/* 222 */         this.metrics.incre("FCXRef.lookupTime", System.currentTimeMillis() - startTime);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\FCXRefCallCmd.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */