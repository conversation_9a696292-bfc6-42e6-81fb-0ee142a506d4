package com.opentext.contivo.callcommand.fulcrum.xref.db;

import java.sql.Connection;
import java.sql.SQLException;

public interface DBPool {
  Connection getConnection() throws SQLException;
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\db\DBPool.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */