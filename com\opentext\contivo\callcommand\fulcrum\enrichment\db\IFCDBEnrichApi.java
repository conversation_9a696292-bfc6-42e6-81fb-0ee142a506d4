package com.opentext.contivo.callcommand.fulcrum.enrichment.db;

import fj.P2;
import java.sql.Connection;
import java.sql.SQLException;

public interface IFCDBEnrichApi {
  P2<String, Long> getDbData(Connection paramConnection, String paramString1, int paramInt, String paramString2, String paramString3, String paramString4, String paramString5) throws Exception;
  
  int updateDbData(Connection paramConnection, String paramString1, int paramInt1, int paramInt2, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) throws Exception;
  
  boolean saveDbData(Connection paramConnection, String paramString1, int paramInt1, int paramInt2, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) throws Exception;
  
  int updateRefNo(Connection paramConnection) throws SQLException;
  
  void insertRefNo(Connection paramConnection) throws SQLException;
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\enrichment\db\IFCDBEnrichApi.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */