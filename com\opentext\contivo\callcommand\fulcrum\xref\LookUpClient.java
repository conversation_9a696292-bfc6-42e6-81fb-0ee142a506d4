package com.opentext.contivo.callcommand.fulcrum.xref;

public interface LookUpClient {
  String lookupValue(String paramString1, String paramString2, String[] paramArrayOfString1, String paramString3, String[] paramArrayOfString2, Op[] paramArrayOfOp, boolean paramBoolean) throws Exception;
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\LookUpClient.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */