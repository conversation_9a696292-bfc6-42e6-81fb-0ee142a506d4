/*    */ package com.opentext.contivo.callcommand.fulcrum.seq;
/*    */ 
/*    */ import java.util.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCSeqProp
/*    */ {
/*    */   private final String solutionID;
/*    */   private final boolean traceMode;
/*    */   
/*    */   public FCSeqProp(String solutionID) {
/* 24 */     this.solutionID = solutionID;
/* 25 */     this.traceMode = false;
/*    */   }
/*    */ 
/*    */   
/*    */   public FCSeqProp(String solutionID, boolean traceMode) {
/* 30 */     this.solutionID = Objects.<String>requireNonNull(solutionID);
/* 31 */     this.traceMode = traceMode;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getSolutionID() {
/* 36 */     return this.solutionID;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean isTraceMode() {
/* 41 */     return this.traceMode;
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\FCSeqProp.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */