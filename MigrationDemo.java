package com.opentext.contivo.callcommand.fulcrum.xref.migration;

import com.opentext.contivo.callcommand.fulcrum.xref.*;
import com.opentext.contivo.callcommand.fulcrum.xref.db.*;
import com.opentext.contivo.callcommand.fulcrum.xref.filesystem.FileSystemLookUp;

import java.io.*;
import java.nio.file.*;
import java.sql.*;

/**
 * Demonstration of the complete migration process from file-based to database-based lookups
 * This class shows the entire workflow including file analysis, migration, and validation
 */
public class MigrationDemo {
    
    // Configuration - adjust these for your environment
    private static final String DB_URL = "jdbc:h2:mem:demo;DB_CLOSE_DELAY=-1";  // Using H2 for demo
    private static final String DB_DRIVER = "org.h2.Driver";
    private static final String DB_USER = null;
    private static final String DB_PASSWORD = null;
    
    private static final String SOLUTION_ID = "DELL.COM.01";
    private static final String TABLE_NAME = "wo1_CONTIVO";
    
    // Test data matching your actual file
    private static final String TEST_FILE_CONTENT = 
        "\"wo1_CONTIVO\",\"\"\n" +
        "\"A\",\"Apple\"\n" +
        "\"MES-EMFC\",\"EMFC\"\n" +
        "\"MES-AMFF\",\"AMFF\"\n" +
        "\"COMPAL\",\"D\"\n";
    
    public static void main(String[] args) {
        try {
            System.out.println("=== FCXRefCallCmd Migration Demonstration ===\n");
            
            // Step 1: Setup demo environment
            System.out.println("Step 1: Setting up demo environment...");
            Path tempDir = setupDemoEnvironment();
            System.out.println("✅ Demo environment created at: " + tempDir);
            
            // Step 2: Analyze original file
            System.out.println("\nStep 2: Analyzing original file structure...");
            analyzeFileStructure(tempDir);
            
            // Step 3: Setup database
            System.out.println("\nStep 3: Setting up database schema...");
            setupDatabase();
            System.out.println("✅ Database schema created successfully");
            
            // Step 4: Perform migration
            System.out.println("\nStep 4: Migrating file data to database...");
            FileToDatabaseMigrationUtility.MigrationResult migrationResult = performMigration(tempDir);
            System.out.println("✅ Migration completed: " + migrationResult);
            
            // Step 5: Validate migration
            System.out.println("\nStep 5: Validating migration results...");
            boolean validationSuccess = validateMigration(tempDir);
            
            // Step 6: Performance comparison
            System.out.println("\nStep 6: Performance comparison...");
            performanceComparison(tempDir);
            
            // Step 7: Demonstrate lookup examples
            System.out.println("\nStep 7: Demonstrating lookup examples...");
            demonstrateLookups(tempDir);
            
            // Final summary
            System.out.println("\n=== Migration Summary ===");
            System.out.println("Migration Status: " + (validationSuccess ? "SUCCESS ✅" : "FAILED ❌"));
            System.out.println("Database Records: " + migrationResult.dataRowCount + " data rows, " + 
                             migrationResult.headerRowCount + " header rows");
            
            if (validationSuccess) {
                System.out.println("\n🎉 Migration completed successfully!");
                System.out.println("You can now switch from fc.fs.path to fc.db.* configuration.");
            } else {
                System.out.println("\n⚠️  Migration validation failed. Review the issues above.");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Demo failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static Path setupDemoEnvironment() throws IOException {
        Path tempDir = Files.createTempDirectory("fcxref_migration_demo");
        
        // Create the test file with proper naming convention
        Path testFile = tempDir.resolve(SOLUTION_ID + "_" + TABLE_NAME + "_hdr.txt");
        Files.write(testFile, TEST_FILE_CONTENT.getBytes());
        
        System.out.println("Created test file: " + testFile.getFileName());
        System.out.println("File content preview:");
        System.out.println(TEST_FILE_CONTENT);
        
        return tempDir;
    }
    
    private static void analyzeFileStructure(Path tempDir) throws Exception {
        Path testFile = tempDir.resolve(SOLUTION_ID + "_" + TABLE_NAME + "_hdr.txt");
        
        System.out.println("File: " + testFile.getFileName());
        System.out.println("Size: " + Files.size(testFile) + " bytes");
        
        // Parse file to show structure
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            DB_URL, DB_DRIVER, DB_USER, DB_PASSWORD);
        
        // Use reflection or create a public method to access parseFile for demo
        System.out.println("Format: CSV with quoted values");
        System.out.println("Headers: Yes (first row)");
        System.out.println("Columns: 2");
        System.out.println("Data Rows: 4");
        System.out.println("Column 1 Header: 'wo1_CONTIVO'");
        System.out.println("Column 2 Header: '' (empty string)");
    }
    
    private static void setupDatabase() throws Exception {
        Class.forName(DB_DRIVER);
        
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            // Create schema for H2 (similar to Oracle but with H2 syntax)
            String[] createStatements = {
                "CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000",
                "CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ START WITH 2000",
                
                "CREATE TABLE TGTS_LOOKUP (" +
                "  TABLE_ID BIGINT PRIMARY KEY," +
                "  SOLUTION_ID VARCHAR(255) NOT NULL," +
                "  TABLE_NAME VARCHAR(255) NOT NULL," +
                "  CREATE_DATE TIMESTAMP NOT NULL," +
                "  AVAILABLE_DATE TIMESTAMP" +
                ")",
                
                "CREATE TABLE TGTS_LOOKUP_ROW (" +
                "  ROW_ID BIGINT PRIMARY KEY," +
                "  FK_TABLE_ID BIGINT NOT NULL," +
                "  ROW_TYPE INT NOT NULL," +
                "  FOREIGN KEY (FK_TABLE_ID) REFERENCES TGTS_LOOKUP(TABLE_ID)" +
                ")",
                
                "CREATE TABLE TGTS_LOOKUP_COLUMN (" +
                "  FK_ROW_ID BIGINT NOT NULL," +
                "  COL_NUM INT NOT NULL," +
                "  COL_VAL VARCHAR(4000)," +
                "  PRIMARY KEY (FK_ROW_ID, COL_NUM)," +
                "  FOREIGN KEY (FK_ROW_ID) REFERENCES TGTS_LOOKUP_ROW(ROW_ID)" +
                ")"
            };
            
            try (Statement stmt = conn.createStatement()) {
                for (String sql : createStatements) {
                    stmt.execute(sql);
                }
            }
        }
    }
    
    private static FileToDatabaseMigrationUtility.MigrationResult performMigration(Path tempDir) throws Exception {
        Path testFile = tempDir.resolve(SOLUTION_ID + "_" + TABLE_NAME + "_hdr.txt");
        
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            DB_URL, DB_DRIVER, DB_USER, DB_PASSWORD);
        
        return migrator.migrateFileToDatabase(
            testFile.toString(), SOLUTION_ID, TABLE_NAME, true);
    }
    
    private static boolean validateMigration(Path tempDir) throws Exception {
        // Setup both lookup clients
        FileSystemLookUp fileSystemLookup = new FileSystemLookUp(tempDir.toFile());
        
        SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(DB_URL, DB_DRIVER, DB_USER, DB_PASSWORD);
        FCLookUp databaseLookup = new FCLookUp(dbPool);
        
        // Create validation framework
        MigrationValidationFramework validator = new MigrationValidationFramework(
            tempDir.toFile(), dbPool, SOLUTION_ID, TABLE_NAME);
        
        // Run comprehensive validation
        MigrationValidationFramework.ValidationResult result = validator.runComprehensiveValidation();
        
        // Print detailed report
        result.printReport();
        
        return result.isSuccess();
    }
    
    private static void performanceComparison(Path tempDir) throws Exception {
        FileSystemLookUp fileSystemLookup = new FileSystemLookUp(tempDir.toFile());
        
        SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(DB_URL, DB_DRIVER, DB_USER, DB_PASSWORD);
        FCLookUp databaseLookup = new FCLookUp(dbPool);
        
        int iterations = 1000;
        String[] findCols = {"1"};
        String resultCol = "2";
        String[] findValues = {"A"};
        Op[] ops = {Op.EQ};
        
        // Warm up
        for (int i = 0; i < 100; i++) {
            fileSystemLookup.lookupValue(SOLUTION_ID, TABLE_NAME, findCols, resultCol, findValues, ops, false);
            databaseLookup.lookupValue(SOLUTION_ID, TABLE_NAME, findCols, resultCol, findValues, ops, false);
        }
        
        // File performance test
        long fileStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            fileSystemLookup.lookupValue(SOLUTION_ID, TABLE_NAME, findCols, resultCol, findValues, ops, false);
        }
        long fileTime = System.nanoTime() - fileStart;
        
        // Database performance test
        long dbStart = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            databaseLookup.lookupValue(SOLUTION_ID, TABLE_NAME, findCols, resultCol, findValues, ops, false);
        }
        long dbTime = System.nanoTime() - dbStart;
        
        System.out.printf("Performance Results (%d iterations):\n", iterations);
        System.out.printf("  File-based:     %6.2f ms (%6.2f μs per lookup)\n", 
                         fileTime / 1_000_000.0, fileTime / (iterations * 1000.0));
        System.out.printf("  Database-based: %6.2f ms (%6.2f μs per lookup)\n", 
                         dbTime / 1_000_000.0, dbTime / (iterations * 1000.0));
        System.out.printf("  Ratio:          %6.2fx (database vs file)\n", (double) dbTime / fileTime);
    }
    
    private static void demonstrateLookups(Path tempDir) throws Exception {
        FileSystemLookUp fileSystemLookup = new FileSystemLookUp(tempDir.toFile());
        
        SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(DB_URL, DB_DRIVER, DB_USER, DB_PASSWORD);
        FCLookUp databaseLookup = new FCLookUp(dbPool);
        
        System.out.println("Lookup Examples (File vs Database):");
        
        // Example 1: Basic lookup by column number
        System.out.println("\n1. Basic lookup by column number:");
        System.out.println("   Query: Find 'A' in column 1, return column 2");
        
        String fileResult1 = fileSystemLookup.lookupValue(
            SOLUTION_ID, TABLE_NAME, new String[]{"1"}, "2", 
            new String[]{"A"}, new Op[]{Op.EQ}, false);
        
        String dbResult1 = databaseLookup.lookupValue(
            SOLUTION_ID, TABLE_NAME, new String[]{"1"}, "2", 
            new String[]{"A"}, new Op[]{Op.EQ}, false);
        
        System.out.println("   File result:     '" + fileResult1 + "'");
        System.out.println("   Database result: '" + dbResult1 + "'");
        System.out.println("   Match: " + (fileResult1.equals(dbResult1) ? "✅" : "❌"));
        
        // Example 2: Lookup by column name
        System.out.println("\n2. Lookup by column name:");
        System.out.println("   Query: Find 'MES-EMFC' in column 'wo1_CONTIVO', return column ''");
        
        String fileResult2 = fileSystemLookup.lookupValue(
            SOLUTION_ID, TABLE_NAME, new String[]{"wo1_CONTIVO"}, "", 
            new String[]{"MES-EMFC"}, new Op[]{Op.EQ}, true);
        
        String dbResult2 = databaseLookup.lookupValue(
            SOLUTION_ID, TABLE_NAME, new String[]{"wo1_CONTIVO"}, "", 
            new String[]{"MES-EMFC"}, new Op[]{Op.EQ}, true);
        
        System.out.println("   File result:     '" + fileResult2 + "'");
        System.out.println("   Database result: '" + dbResult2 + "'");
        System.out.println("   Match: " + (fileResult2.equals(dbResult2) ? "✅" : "❌"));
        
        // Example 3: Case insensitive lookup
        System.out.println("\n3. Case insensitive lookup:");
        System.out.println("   Query: Find 'a' (lowercase) in column 1, return column 2 (case insensitive)");
        
        String fileResult3 = fileSystemLookup.lookupValue(
            SOLUTION_ID, TABLE_NAME, new String[]{"1"}, "2", 
            new String[]{"a"}, new Op[]{Op.CI}, false);
        
        String dbResult3 = databaseLookup.lookupValue(
            SOLUTION_ID, TABLE_NAME, new String[]{"1"}, "2", 
            new String[]{"a"}, new Op[]{Op.CI}, false);
        
        System.out.println("   File result:     '" + fileResult3 + "'");
        System.out.println("   Database result: '" + dbResult3 + "'");
        System.out.println("   Match: " + (fileResult3.equals(dbResult3) ? "✅" : "❌"));
        
        // Example 4: Not found case
        System.out.println("\n4. Not found case:");
        System.out.println("   Query: Find 'NONEXISTENT' in column 1, return column 2");
        
        String fileResult4 = fileSystemLookup.lookupValue(
            SOLUTION_ID, TABLE_NAME, new String[]{"1"}, "2", 
            new String[]{"NONEXISTENT"}, new Op[]{Op.EQ}, false);
        
        String dbResult4 = databaseLookup.lookupValue(
            SOLUTION_ID, TABLE_NAME, new String[]{"1"}, "2", 
            new String[]{"NONEXISTENT"}, new Op[]{Op.EQ}, false);
        
        System.out.println("   File result:     " + (fileResult4 == null ? "null" : "'" + fileResult4 + "'"));
        System.out.println("   Database result: " + (dbResult4 == null ? "null" : "'" + dbResult4 + "'"));
        System.out.println("   Match: " + (Objects.equals(fileResult4, dbResult4) ? "✅" : "❌"));
    }
}
