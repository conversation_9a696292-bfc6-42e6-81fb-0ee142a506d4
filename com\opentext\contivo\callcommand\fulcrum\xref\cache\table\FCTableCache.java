package com.opentext.contivo.callcommand.fulcrum.xref.cache.table;

import com.opentext.contivo.callcommand.fulcrum.xref.Table;

public interface FCTableCache {
  Table get(FCTableCacheKey paramFCTableCacheKey);
  
  void put(FCTableCacheKey paramFCTableCacheKey, Table paramTable);
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\cache\table\FCTableCache.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */