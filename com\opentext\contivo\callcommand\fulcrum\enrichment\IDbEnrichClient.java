package com.opentext.contivo.callcommand.fulcrum.enrichment;

public interface IDbEnrichClient {
  boolean saveDbData(String paramString1, int paramInt1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, int paramInt2, int paramInt3) throws Exception;
  
  String getDbData(String paramString1, int paramInt1, String paramString2, String paramString3, String paramString4, String paramString5, int paramInt2) throws Exception;
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\enrichment\IDbEnrichClient.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */