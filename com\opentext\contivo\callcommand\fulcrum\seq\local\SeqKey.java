/*    */ package com.opentext.contivo.callcommand.fulcrum.seq.local;
/*    */ 
/*    */ import com.google.common.base.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SeqKey
/*    */ {
/*    */   private final String key1;
/*    */   private final String key2;
/*    */   private final String key3;
/*    */   private final String key4;
/*    */   private final int hashCode;
/*    */   
/*    */   public SeqKey(String key1, String key2, String key3, String key4) {
/* 27 */     this.key1 = key1;
/* 28 */     this.key2 = key2;
/* 29 */     this.key3 = key3;
/* 30 */     this.key4 = key4;
/* 31 */     int hash = key1.hashCode();
/* 32 */     hash ^= (key2 != null) ? key2.hashCode() : 0;
/* 33 */     hash ^= (key3 != null) ? key3.hashCode() : 0;
/* 34 */     hash ^= (key4 != null) ? key4.hashCode() : 0;
/* 35 */     this.hashCode = hash;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean equals(Object other) {
/* 41 */     if (this == other)
/* 42 */       return true; 
/* 43 */     if (other == null)
/* 44 */       return false; 
/* 45 */     if (!(other instanceof SeqKey)) {
/* 46 */       return false;
/*    */     }
/* 48 */     SeqKey otherKey = (SeqKey)other;
/* 49 */     boolean sameKey1 = Objects.equal(this.key1, otherKey.key1);
/* 50 */     boolean sameKey2 = Objects.equal(this.key2, otherKey.key2);
/* 51 */     boolean sameKey3 = Objects.equal(this.key3, otherKey.key3);
/* 52 */     boolean sameKey4 = Objects.equal(this.key4, otherKey.key4);
/* 53 */     return (sameKey1 && sameKey2 && sameKey3 && sameKey4);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 60 */     return this.hashCode;
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\local\SeqKey.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */