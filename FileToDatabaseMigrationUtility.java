package com.opentext.contivo.callcommand.fulcrum.xref.migration;

import java.io.*;
import java.sql.*;
import java.util.*;
import java.util.Date;

/**
 * Utility class to migrate file-based lookup data to database tables
 * Maintains identical lookup behavior between fc.fs.path and fc.db.* configurations
 */
public class FileToDatabaseMigrationUtility {
    
    // Database connection parameters
    private final String dbUrl;
    private final String dbDriver;
    private final String dbUser;
    private final String dbPassword;

    // Database type detection
    private enum DatabaseType {
        ORACLE, H2, UNKNOWN
    }

    private DatabaseType detectDatabaseType(Connection conn) throws SQLException {
        String productName = conn.getMetaData().getDatabaseProductName().toLowerCase();
        if (productName.contains("oracle")) {
            return DatabaseType.ORACLE;
        } else if (productName.contains("h2")) {
            return DatabaseType.H2;
        } else {
            return DatabaseType.UNKNOWN;
        }
    }
    
    public FileToDatabaseMigrationUtility(String dbUrl, String dbDriver, String dbUser, String dbPassword) {
        this.dbUrl = dbUrl;
        this.dbDriver = dbDriver;
        this.dbUser = dbUser;
        this.dbPassword = dbPassword;
    }
    
    /**
     * Migrates a file to database tables
     * @param filePath Path to the source file
     * @param solutionId Solution ID for database partitioning
     * @param tableName Logical table name
     * @param hasHeaders Whether the file contains header row
     * @return Migration result with statistics
     */
    public MigrationResult migrateFileToDatabase(String filePath, String solutionId, 
                                               String tableName, boolean hasHeaders) throws Exception {
        
        // Parse file using same logic as FileSystemLookUp
        ParsedFileData fileData = parseFile(new File(filePath), hasHeaders);
        
        // Insert into database
        return insertIntoDatabase(fileData, solutionId, tableName);
    }
    
    /**
     * Parses file using identical logic to TableParser.parse()
     */
    private ParsedFileData parseFile(File tableFile, boolean hasColHeaders) throws Exception {
        BufferedReader reader = null;
        Map<String, Integer> colNameToNumber = new HashMap<>();
        
        try {
            InputStream stream = new BufferedInputStream(new FileInputStream(tableFile));
            reader = new BufferedReader(new InputStreamReader(stream));
            List<Map<Integer, String>> rows = new ArrayList<>();
            rows.add(new HashMap<>());
            
            int next = -1;
            StringBuilder builder = new StringBuilder();
            boolean inQuotes = false;
            boolean expectDelimiterOrQuote = false;
            int currentColIndex = 1;
            boolean processingHeader = hasColHeaders;
            
            // Identical parsing logic to TableParser
            while ((next = reader.read()) != -1) {
                char nextChar = (char) next;
                
                if (expectDelimiterOrQuote) {
                    if (nextChar == '"') {
                        builder.append(nextChar);
                        inQuotes = true;
                        expectDelimiterOrQuote = false;
                        continue;
                    }
                    if (nextChar != ',' && nextChar != '\r' && nextChar != '\n') {
                        throw new Exception("IllegalCharacter " + nextChar + ". Expected ',' or delimiter.");
                    }
                    expectDelimiterOrQuote = false;
                }
                
                switch (nextChar) {
                    case '\r':
                        if (inQuotes) {
                            builder.append(nextChar);
                        }
                        continue;
                    case '\n':
                        if (!inQuotes) {
                            if (processingHeader) {
                                colNameToNumber.put(builder.toString(), currentColIndex);
                                processingHeader = false;
                            } else {
                                rows.get(rows.size() - 1).put(currentColIndex, builder.toString());
                                rows.add(new HashMap<>());
                            }
                            currentColIndex = 1;
                            builder.setLength(0);
                            continue;
                        }
                        builder.append(nextChar);
                        continue;
                    case ',':
                        if (!inQuotes) {
                            if (processingHeader) {
                                colNameToNumber.put(builder.toString(), currentColIndex);
                            } else {
                                rows.get(rows.size() - 1).put(currentColIndex, builder.toString());
                            }
                            currentColIndex++;
                            builder.setLength(0);
                            continue;
                        }
                        builder.append(nextChar);
                        continue;
                    case '"':
                        if (inQuotes) {
                            inQuotes = false;
                            if (builder.length() == 0) {
                                builder.append(nextChar);
                                continue;
                            }
                            expectDelimiterOrQuote = true;
                            continue;
                        }
                        inQuotes = true;
                        continue;
                }
                builder.append(nextChar);
            }
            
            // Handle final column
            if (builder.length() > 0) {
                rows.get(rows.size() - 1).put(currentColIndex, builder.toString());
            }
            
            // Remove empty last row if exists
            if (rows.size() > 0 && rows.get(rows.size() - 1).isEmpty()) {
                rows.remove(rows.size() - 1);
            }
            
            return new ParsedFileData(colNameToNumber, rows, hasColHeaders);
            
        } catch (Exception e) {
            throw new Exception("Unable to parse table: " + e.getMessage());
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    // Ignore
                }
            }
        }
    }
    
    /**
     * Inserts parsed file data into database tables
     */
    private MigrationResult insertIntoDatabase(ParsedFileData fileData, String solutionId, 
                                             String tableName) throws Exception {
        
        Connection conn = null;
        try {
            // Load driver and create connection
            Class.forName(dbDriver);
            Properties props = new Properties();
            if (dbUser != null) props.put("user", dbUser);
            if (dbPassword != null) props.put("password", dbPassword);
            
            conn = DriverManager.getConnection(dbUrl, props);
            conn.setAutoCommit(false);
            
            // Insert into TGTS_LOOKUP
            long tableId = insertLookupTable(conn, solutionId, tableName);
            
            // Insert header row if exists
            if (fileData.hasHeaders && !fileData.headers.isEmpty()) {
                long headerRowId = insertLookupRow(conn, tableId, 1); // ROW_TYPE=1 for headers

                // Convert headers map from (column name → column number) to (column number → column name)
                Map<Integer, String> headerColumns = new HashMap<>();
                for (Map.Entry<String, Integer> entry : fileData.headers.entrySet()) {
                    headerColumns.put(entry.getValue(), entry.getKey());
                }

                insertLookupColumns(conn, headerRowId, headerColumns);
            }
            
            // Insert data rows
            int dataRowCount = 0;
            for (Map<Integer, String> row : fileData.dataRows) {
                if (!row.isEmpty()) {
                    long dataRowId = insertLookupRow(conn, tableId, 0); // ROW_TYPE=0 for data
                    insertLookupColumns(conn, dataRowId, row);
                    dataRowCount++;
                }
            }
            
            conn.commit();
            
            return new MigrationResult(tableId, dataRowCount, fileData.hasHeaders ? 1 : 0);
            
        } catch (Exception e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    // Ignore rollback exception
                }
            }
            throw e;
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    // Ignore close exception
                }
            }
        }
    }
    
    private long insertLookupTable(Connection conn, String solutionId, String tableName) throws SQLException {
        DatabaseType dbType = detectDatabaseType(conn);
        String sequenceExpression;

        switch (dbType) {
            case ORACLE:
                sequenceExpression = "TGTS_LOOKUP_SEQ.NEXTVAL";
                break;
            case H2:
                sequenceExpression = "NEXT VALUE FOR TGTS_LOOKUP_SEQ";
                break;
            default:
                // Default to Oracle syntax for unknown databases
                sequenceExpression = "TGTS_LOOKUP_SEQ.NEXTVAL";
                break;
        }

        String sql = "INSERT INTO TGTS_LOOKUP (TABLE_ID, SOLUTION_ID, TABLE_NAME, CREATE_DATE, AVAILABLE_DATE) " +
                    "VALUES (" + sequenceExpression + ", ?, ?, ?, NULL)";

        try (PreparedStatement stmt = conn.prepareStatement(sql, new String[]{"TABLE_ID"})) {
            stmt.setString(1, solutionId);
            stmt.setString(2, tableName);
            stmt.setTimestamp(3, new Timestamp(new Date().getTime()));

            stmt.executeUpdate();

            try (ResultSet rs = stmt.getGeneratedKeys()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
                throw new SQLException("Failed to get generated TABLE_ID");
            }
        }
    }
    
    private long insertLookupRow(Connection conn, long tableId, int rowType) throws SQLException {
        DatabaseType dbType = detectDatabaseType(conn);
        String sequenceExpression;

        switch (dbType) {
            case ORACLE:
                sequenceExpression = "TGTS_LOOKUP_ROW_SEQ.NEXTVAL";
                break;
            case H2:
                sequenceExpression = "NEXT VALUE FOR TGTS_LOOKUP_ROW_SEQ";
                break;
            default:
                // Default to Oracle syntax for unknown databases
                sequenceExpression = "TGTS_LOOKUP_ROW_SEQ.NEXTVAL";
                break;
        }

        String sql = "INSERT INTO TGTS_LOOKUP_ROW (ROW_ID, FK_TABLE_ID, ROW_TYPE) " +
                    "VALUES (" + sequenceExpression + ", ?, ?)";

        try (PreparedStatement stmt = conn.prepareStatement(sql, new String[]{"ROW_ID"})) {
            stmt.setLong(1, tableId);
            stmt.setInt(2, rowType);

            stmt.executeUpdate();

            try (ResultSet rs = stmt.getGeneratedKeys()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
                throw new SQLException("Failed to get generated ROW_ID");
            }
        }
    }
    
    private void insertLookupColumns(Connection conn, long rowId, Map<Integer, String> columns) throws SQLException {
        String sql = "INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (?, ?, ?)";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            for (Map.Entry<Integer, String> entry : columns.entrySet()) {
                stmt.setLong(1, rowId);
                stmt.setInt(2, entry.getKey());
                stmt.setString(3, entry.getValue());
                stmt.addBatch();
            }
            stmt.executeBatch();
        }
    }
    
    // Helper classes
    public static class ParsedFileData {
        public final Map<String, Integer> headers;
        public final List<Map<Integer, String>> dataRows;
        public final boolean hasHeaders;
        
        public ParsedFileData(Map<String, Integer> headers, List<Map<Integer, String>> dataRows, boolean hasHeaders) {
            this.headers = headers;
            this.dataRows = dataRows;
            this.hasHeaders = hasHeaders;
        }
    }
    
    public static class MigrationResult {
        public final long tableId;
        public final int dataRowCount;
        public final int headerRowCount;
        
        public MigrationResult(long tableId, int dataRowCount, int headerRowCount) {
            this.tableId = tableId;
            this.dataRowCount = dataRowCount;
            this.headerRowCount = headerRowCount;
        }
        
        @Override
        public String toString() {
            return String.format("Migration completed: tableId=%d, dataRows=%d, headerRows=%d", 
                               tableId, dataRowCount, headerRowCount);
        }
    }
}
