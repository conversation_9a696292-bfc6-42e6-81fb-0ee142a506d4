/*    */ package com.opentext.contivo.callcommand.fulcrum.seq.db;
/*    */ 
/*    */ import java.sql.CallableStatement;
/*    */ import java.sql.Connection;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCDBSeqOracleApi
/*    */   extends FCDBSeqApi
/*    */ {
/*    */   public long incrementSequenceNumberAndGet(Connection conn, long increment, String key1, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/* 21 */     if (key1 == null)
/*    */     {
/* 23 */       throw new Exception("First key must not be null");
/*    */     }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 30 */     String sqlStmt = "BEGIN UPDATE EC_SEQUENCE_NUMBER SET  LAST_SEQUENCE_NUMBER = LAST_SEQUENCE_NUMBER + ? WHERE SEQUENCE_KEY1 = ?  AND SEQUENCE_KEY2" + ((optionalKey1 != null) ? " = ? " : " IS NULL") + " AND SEQUENCE_KEY3" + ((optionalKey2 != null) ? " = ? " : " IS NULL") + " AND SEQUENCE_KEY4" + ((optionalKey3 != null) ? " = ? " : " IS NULL") + " RETURNING EC_SEQUENCE_NUMBER.LAST_SEQUENCE_NUMBER INTO ?; END;";
/*    */     
/* 32 */     if (increment != 0L && increment != 1L) {
/* 33 */       throw new Exception("Increment value must be 0 or 1");
/*    */     }
/*    */     
/* 36 */     CallableStatement pStmt = null;
/* 37 */     int result = 0;
/*    */     
/*    */     try {
/* 40 */       pStmt = conn.prepareCall(sqlStmt, 1004, 1007);
/*    */ 
/*    */       
/* 43 */       int i = 1;
/* 44 */       pStmt.setLong(i++, increment);
/* 45 */       pStmt.setString(i++, key1);
/*    */       
/* 47 */       if (optionalKey1 != null) {
/* 48 */         pStmt.setString(i++, optionalKey1);
/*    */       }
/* 50 */       if (optionalKey2 != null) {
/* 51 */         pStmt.setString(i++, optionalKey2);
/*    */       }
/* 53 */       if (optionalKey3 != null) {
/* 54 */         pStmt.setString(i++, optionalKey3);
/*    */       }
/* 56 */       pStmt.registerOutParameter(i++, 4);
/* 57 */       pStmt.execute();
/* 58 */       result = pStmt.getInt(i - 1);
/*    */     }
/*    */     finally {
/*    */       
/* 62 */       if (pStmt != null)
/*    */       {
/* 64 */         pStmt.close();
/*    */       }
/*    */     } 
/* 67 */     return result;
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\db\FCDBSeqOracleApi.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */