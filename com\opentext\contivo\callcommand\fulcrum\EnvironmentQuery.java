/*     */ package com.opentext.contivo.callcommand.fulcrum;
/*     */ 
/*     */ import com.contivo.analyst.util.ContivoExternalClass;
/*     */ import com.contivo.analyst.util.ContivoExternalMethod;
/*     */ import com.contivo.mixedruntime.RuntimeMessageException;
/*     */ import com.contivo.runtime.core.IContivoRuntime;
/*     */ import com.contivo.runtime.core.IContivoRuntimeSelfTimeout;
/*     */ import com.contivo.runtime.core.TRuntimeEngineException;
/*     */ import com.contivo.transform.MessageInfo;
/*     */ import com.contivo.transform.Tag;
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.nio.file.Files;
/*     */ import java.util.Optional;
/*     */ import java.util.Properties;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @ContivoExternalClass
/*     */ public class EnvironmentQuery
/*     */   implements IContivoRuntimeSelfTimeout
/*     */ {
/*     */   private static final String ENV_STRING = "ENV";
/*     */   private static final String LOCAL_STRING = "local";
/*     */   private static final String SOLUTION_ID = "solutionID";
/*     */   private static final String SENDERID = "senderID";
/*     */   private static final String RECEIVERID = "receiverID";
/*     */   private static final String DOCTYPE = "docType";
/*     */   private static final String SNRF = "snrf";
/*     */   public static final String SOLUTION_ID_PROP = "fc.solutionID";
/*     */   public static final String ENV_FS_PROP = "fc.env.prop.path";
/*     */   private EnvProp envProps;
/*     */   
/*     */   public static Properties getEnvJavaProp(IContivoRuntime icr) {
/*  44 */     MessageInfo info = new MessageInfo("EnvProp.init", new Tag[] { Tag.FATAL });
/*     */     
/*  46 */     Properties prop = icr.getUserProperties();
/*     */     
/*  48 */     String fsPath = prop.getProperty("fc.env.prop.path");
/*  49 */     if (fsPath != null) {
/*     */       try {
/*  51 */         Properties envJavaProp = new Properties();
/*     */         
/*  53 */         envJavaProp.load(Files.newInputStream((new File(fsPath)).toPath(), new java.nio.file.OpenOption[0]));
/*  54 */         return envJavaProp;
/*  55 */       } catch (IOException e) {
/*  56 */         throw new RuntimeMessageException(info, "Please specify the fc.env.prop.path in Tools->Options->User Properties Manager");
/*     */       } 
/*     */     }
/*  59 */     return null;
/*     */   }
/*     */   
/*     */   public static EnvProp getEnvProp(IContivoRuntime icr, Properties prop) {
/*  63 */     EnvProp envProps = (EnvProp)icr.getInstance(EnvProp.class);
/*     */     
/*  65 */     if (envProps == null && prop != null) {
/*     */       
/*  67 */       String solutionId = (String)Optional.<String>ofNullable(prop.getProperty("solutionID")).orElseThrow(() -> new RuntimeException("Missing solutionID in properties file"));
/*     */       
/*  69 */       String senderId = (String)Optional.<String>ofNullable(prop.getProperty("senderID")).orElseThrow(() -> new RuntimeException("Missing senderID in properties file"));
/*     */       
/*  71 */       String receiverId = (String)Optional.<String>ofNullable(prop.getProperty("receiverID")).orElseThrow(() -> new RuntimeException("Missing receiverID in properties file"));
/*     */       
/*  73 */       String docType = (String)Optional.<String>ofNullable(prop.getProperty("docType")).orElseThrow(() -> new RuntimeException("Missing docType in properties file"));
/*     */       
/*  75 */       String snrf = (String)Optional.<String>ofNullable(prop.getProperty("snrf")).orElseThrow(() -> new RuntimeException("Missing snrf in properties file"));
/*     */       
/*  77 */       envProps = new EnvProp(solutionId, senderId, receiverId, docType, snrf);
/*     */     } 
/*     */ 
/*     */     
/*  81 */     return envProps;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void init(IContivoRuntime icr) throws RuntimeMessageException {
/*  90 */     if (this.envProps != null) {
/*     */       return;
/*     */     }
/*  93 */     MessageInfo info = new MessageInfo("EnvironmentQuery.init", new Tag[] { Tag.FATAL });
/*     */     
/*  95 */     this.envProps = getEnvProp(icr, getEnvJavaProp(icr));
/*     */     
/*  97 */     if (this.envProps == null) {
/*  98 */       throw new RuntimeMessageException(info, "Failed to initialize");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getEnvironment")
/*     */   public String getEnvironment(IContivoRuntime icr) throws TRuntimeEngineException {
/* 106 */     String env = System.getenv("ENV");
/* 107 */     return (env == null) ? "local" : env;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getSolutionID")
/*     */   public String getSolutionID(IContivoRuntime icr) throws TRuntimeEngineException {
/* 114 */     init(icr);
/* 115 */     return this.envProps.getSolutionID();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getSenderID")
/*     */   public String getSenderID(IContivoRuntime icr) throws TRuntimeEngineException {
/* 122 */     init(icr);
/* 123 */     return this.envProps.getSenderID();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getReceiverID")
/*     */   public String getReceiverID(IContivoRuntime icr) throws TRuntimeEngineException {
/* 130 */     init(icr);
/* 131 */     return this.envProps.getReceiverID();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDocType")
/*     */   public String getDocType(IContivoRuntime icr) throws TRuntimeEngineException {
/* 138 */     init(icr);
/* 139 */     return this.envProps.getDocType();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getSNRF")
/*     */   public String getSNRF(IContivoRuntime icr) throws TRuntimeEngineException {
/* 146 */     init(icr);
/* 147 */     return this.envProps.getSNRF();
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\EnvironmentQuery.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */