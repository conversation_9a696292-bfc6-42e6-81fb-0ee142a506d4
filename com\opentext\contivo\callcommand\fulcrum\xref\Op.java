/*    */ package com.opentext.contivo.callcommand.fulcrum.xref;
/*    */ 
/*    */ public enum Op {
/*  4 */   EQ("="),
/*  5 */   NOT_EQ("<>"),
/*  6 */   CI("I=");
/*    */   private String rep;
/*    */   
/*    */   Op(String rep) {
/* 10 */     this.rep = rep;
/*    */   }
/*    */   
/*    */   public String getRep() {
/* 14 */     return this.rep;
/*    */   }
/*    */   
/*    */   public static Op fromString(String op) throws Exception {
/* 18 */     switch (op) { case "=":
/* 19 */         return EQ;
/* 20 */       case "<>": return NOT_EQ;
/* 21 */       case "I=": return CI; }
/* 22 */      throw new Exception("Invalid operation. Valid operations are [\"=\", \"<>\", \"I=\"]");
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\Op.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */