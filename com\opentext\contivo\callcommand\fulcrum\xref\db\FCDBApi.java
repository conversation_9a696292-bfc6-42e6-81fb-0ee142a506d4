/*     */ package com.opentext.contivo.callcommand.fulcrum.xref.db;
/*     */ 
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.Op;
/*     */ import java.io.IOException;
/*     */ import java.sql.Connection;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.Timestamp;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FCDBApi
/*     */ {
/*     */   public static final String TABLE_ID = "table_id";
/*     */   public static final String SOLUTION_ID = "solution_id";
/*     */   public static final String TABLE_NAME = "table_name";
/*     */   public static final String AVAILABLE_DATE = "available_date";
/*     */   public static final String CREATE_DATE = "create_date";
/*     */   public static final String HLFLD_TGTS_LOOKUP = "tgts_lookup";
/*     */   public static final String ROW_ID = "row_id";
/*     */   public static final String FK_TABLE_ID = "fk_table_id";
/*     */   public static final String HLFLD_TGTS_LOOKUP_ROW = "tgts_lookup_row";
/*     */   public static final String FK_ROW_ID = "fk_row_id";
/*     */   public static final String COL_NUM = "col_num";
/*     */   public static final String COL_NAME = "col_name";
/*     */   public static final String COL_VAL = "col_val";
/*     */   public static final String HLFLD_TGTS_LOOKUP_COLUMN = "tgts_lookup_column";
/*     */   private static final String TABLE_QUERY = " SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date";
/*     */   private static final String ROWS_QUERY = " SELECT row_id FROM tgts_lookup_row WHERE fk_table_id in ( SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date) AND ROW_TYPE=0 AND ROWNUM <= ? ";
/*     */   private static final String COLS_QUERY = " SELECT col_num, col_val, fk_row_id FROM tgts_lookup_column WHERE fk_row_id in ( SELECT row_id FROM tgts_lookup_row WHERE fk_table_id in ( SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date) AND ROW_TYPE=0 AND ROWNUM <= ? )";
/*     */   private static final String HEADER_ROWS_QUERY = " SELECT row_id FROM tgts_lookup_row WHERE fk_table_id in ( SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date) AND ROW_TYPE=1 ";
/*     */   private static final String HEADERS_QUERY = " SELECT col_num, col_val FROM tgts_lookup_column WHERE fk_row_id in (  SELECT row_id FROM tgts_lookup_row WHERE fk_table_id in ( SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date) AND ROW_TYPE=1  )";
/*     */   private static final String VALUE_QUERY_BASE = " SELECT lookup1.table_id, colRet.col_val FROM tgts_lookup lookup1,tgts_lookup_row row1,%s tgts_lookup_column colRet where lookup1.solution_id=? AND lookup1.table_name=? AND lookup1.table_id=row1.fk_table_id AND %s  ORDER BY lookup1.create_date desc";
/*     */   private static final String TBL_WITH_VALUE_QUERY = " SELECT tbl.table_id, cl.col_val FROM ( SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date) tbl LEFT JOIN (%s) cl ON tbl.table_id = cl.table_id";
/*     */   
/*     */   private static String generateValueQuery(Op[] ops) {
/* 124 */     StringBuilder whereCols = new StringBuilder();
/* 125 */     StringBuilder conditions = new StringBuilder();
/* 126 */     for (int i = 0; i < ops.length; i++) {
/* 127 */       whereCols.append("tgts_lookup_column col" + i + ",");
/* 128 */       conditions.append("row1.row_id=col" + i + ".fk_row_id AND ");
/* 129 */       switch (ops[i]) {
/*     */         case EQ:
/* 131 */           conditions.append("col" + i + ".col_num=? AND col" + i + ".col_val=? AND ");
/*     */           break;
/*     */         case NOT_EQ:
/* 134 */           conditions.append("col" + i + ".col_num=? AND NOT col" + i + ".col_val=? AND ");
/*     */           break;
/*     */         case CI:
/* 137 */           conditions.append("col" + i + ".col_num=? AND lower(col" + i + ".col_val)=lower(?) AND ");
/*     */           break;
/*     */       } 
/*     */     } 
/* 141 */     conditions.append("row1.row_type=0 AND row1.row_id=colRet.fk_row_id AND colRet.col_num=? ");
/*     */     
/* 143 */     return String.format(" SELECT lookup1.table_id, colRet.col_val FROM tgts_lookup lookup1,tgts_lookup_row row1,%s tgts_lookup_column colRet where lookup1.solution_id=? AND lookup1.table_name=? AND lookup1.table_id=row1.fk_table_id AND %s  ORDER BY lookup1.create_date desc", new Object[] { whereCols.toString(), conditions.toString() });
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map<String, Integer> getHeaders(Connection conn, String solutionID, String tableName) throws SQLException {
/* 149 */     PreparedStatement pStmt = null;
/* 150 */     ResultSet rset = null;
/*     */     
/*     */     try {
/* 153 */       pStmt = conn.prepareStatement(" SELECT col_num, col_val FROM tgts_lookup_column WHERE fk_row_id in (  SELECT row_id FROM tgts_lookup_row WHERE fk_table_id in ( SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date) AND ROW_TYPE=1  )", 1004, 1007);
/* 154 */       pStmt.setString(1, solutionID);
/* 155 */       pStmt.setString(2, tableName);
/* 156 */       Date currentDate = new Date();
/* 157 */       pStmt.setTimestamp(3, new Timestamp(currentDate.getTime()));
/* 158 */       rset = pStmt.executeQuery();
/*     */       
/* 160 */       Map<String, Integer> headers = new HashMap<>();
/* 161 */       while (rset.next()) {
/* 162 */         int colNum = rset.getInt(1);
/* 163 */         String colVal = rset.getString(2);
/* 164 */         headers.put(colVal, Integer.valueOf(colNum));
/*     */       } 
/*     */       
/* 167 */       return headers;
/*     */     } finally {
/* 169 */       if (rset != null) {
/* 170 */         rset.close();
/*     */       }
/* 172 */       if (pStmt != null) {
/* 173 */         pStmt.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<Map<Integer, String>> getTable(Connection conn, String solutionID, String tableName, int threshold) throws SQLException, IOException {
/* 191 */     PreparedStatement pStmt = null;
/* 192 */     ResultSet rset = null;
/*     */ 
/*     */     
/*     */     try {
/* 196 */       pStmt = conn.prepareStatement(" SELECT col_num, col_val, fk_row_id FROM tgts_lookup_column WHERE fk_row_id in ( SELECT row_id FROM tgts_lookup_row WHERE fk_table_id in ( SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date) AND ROW_TYPE=0 AND ROWNUM <= ? )", 1004, 1007);
/* 197 */       pStmt.setString(1, solutionID);
/* 198 */       pStmt.setString(2, tableName);
/* 199 */       Date currentDate = new Date();
/* 200 */       pStmt.setTimestamp(3, new Timestamp(currentDate.getTime()));
/* 201 */       pStmt.setInt(4, threshold);
/* 202 */       pStmt.setFetchSize(threshold);
/* 203 */       rset = pStmt.executeQuery();
/*     */       
/* 205 */       Map<Long, Map<Integer, String>> rows = new HashMap<>();
/* 206 */       while (rset.next()) {
/*     */         
/* 208 */         int colNum = rset.getInt(1);
/* 209 */         String colVal = rset.getString(2);
/* 210 */         long rowId = rset.getLong(3);
/* 211 */         Map<Integer, String> currentRow = rows.get(Long.valueOf(rowId));
/* 212 */         if (currentRow == null) {
/*     */           
/* 214 */           currentRow = new HashMap<>();
/* 215 */           rows.put(Long.valueOf(rowId), currentRow);
/*     */         } 
/* 217 */         currentRow.put(Integer.valueOf(colNum), colVal);
/*     */       } 
/*     */       
/* 220 */       return new ArrayList(rows.values());
/*     */     }
/*     */     finally {
/*     */       
/* 224 */       if (rset != null)
/*     */       {
/* 226 */         rset.close();
/*     */       }
/* 228 */       if (pStmt != null)
/*     */       {
/* 230 */         pStmt.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   public static boolean checkTableExists(Connection conn, String solutionID, String tableName) throws SQLException {
/* 236 */     PreparedStatement pStmt = null;
/* 237 */     ResultSet rset = null;
/*     */ 
/*     */     
/*     */     try {
/* 241 */       pStmt = conn.prepareStatement(" SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date", 1004, 1007);
/* 242 */       pStmt.setString(1, solutionID);
/* 243 */       pStmt.setString(2, tableName);
/* 244 */       Date currentDate = new Date();
/* 245 */       pStmt.setTimestamp(3, new Timestamp(currentDate.getTime()));
/* 246 */       rset = pStmt.executeQuery();
/* 247 */       return rset.next();
/*     */     }
/*     */     finally {
/*     */       
/* 251 */       if (rset != null)
/*     */       {
/* 253 */         rset.close();
/*     */       }
/* 255 */       if (pStmt != null)
/*     */       {
/* 257 */         pStmt.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String selectLookupValue(Connection conn, String solutionID, String tableName, String[] findCol, String resultCol, String[] findValue, Op[] ops) throws Exception {
/* 278 */     PreparedStatement pStmt = null;
/* 279 */     ResultSet rset = null;
/*     */     
/*     */     try {
/* 282 */       pStmt = conn.prepareStatement(String.format(" SELECT tbl.table_id, cl.col_val FROM ( SELECT table_id FROM tgts_lookup lookup  INNER JOIN  (SELECT solution_id,table_name, MAX(create_date) AS max_create_date  FROM tgts_lookup WHERE solution_id=? AND table_name=? AND (available_date<=? OR available_date IS NULL ) GROUP BY solution_id, table_name) grouped_lookup ON lookup.solution_id = grouped_lookup.solution_id AND lookup.table_name = grouped_lookup.table_name AND lookup.create_date = grouped_lookup.max_create_date) tbl LEFT JOIN (%s) cl ON tbl.table_id = cl.table_id", new Object[] { generateValueQuery(ops) }), 1004, 1007);
/*     */       
/* 284 */       pStmt.setString(1, solutionID);
/* 285 */       pStmt.setString(2, tableName);
/* 286 */       Date currentDate = new Date();
/* 287 */       pStmt.setTimestamp(3, new Timestamp(currentDate.getTime()));
/* 288 */       pStmt.setString(4, solutionID);
/* 289 */       pStmt.setString(5, tableName);
/*     */       
/* 291 */       for (int i = 0; i < findCol.length; i++) {
/* 292 */         pStmt.setInt(6 + 2 * i, parseInt(findCol[i], "1 (findCols) index [" + i + 1 + "]").intValue());
/* 293 */         pStmt.setString(7 + 2 * i, findValue[i]);
/*     */       } 
/* 295 */       pStmt.setInt(8 + 2 * (findCol.length - 1), parseInt(resultCol, "2 (resultCol)").intValue());
/*     */       
/* 297 */       pStmt.setFetchSize(1);
/* 298 */       rset = pStmt.executeQuery();
/* 299 */       if (rset.next())
/*     */       {
/* 301 */         return rset.getString(2);
/*     */       }
/*     */       
/* 304 */       throw new Exception("Table '" + tableName + "' in solutionID '" + solutionID + "' does not exist");
/*     */     }
/*     */     finally {
/*     */       
/* 308 */       if (rset != null)
/*     */       {
/* 310 */         rset.close();
/*     */       }
/* 312 */       if (pStmt != null)
/*     */       {
/* 314 */         pStmt.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static Integer parseInt(String stringToParse, String name) throws Exception {
/*     */     try {
/* 323 */       return Integer.valueOf(Integer.parseInt(stringToParse));
/*     */     }
/* 325 */     catch (NumberFormatException e) {
/*     */       
/* 327 */       throw new Exception("Expected a column number (such as '1') for parameter  " + name + ", but found '" + stringToParse + "' instead.");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private static String generateActualSql(String sqlQuery, Object... parameters) {
/* 333 */     String[] parts = sqlQuery.split("\\?");
/* 334 */     StringBuilder sb = new StringBuilder();
/*     */ 
/*     */     
/* 337 */     for (int i = 0; i < parts.length; i++) {
/* 338 */       String part = parts[i];
/* 339 */       sb.append(part);
/* 340 */       if (i < parameters.length) {
/* 341 */         sb.append(formatParameter(parameters[i]));
/*     */       }
/*     */     } 
/*     */     
/* 345 */     return sb.toString();
/*     */   }
/*     */   
/*     */   private static String formatParameter(Object parameter) {
/* 349 */     if (parameter == null) {
/* 350 */       return "NULL";
/*     */     }
/* 352 */     if (parameter instanceof String)
/* 353 */       return "'" + ((String)parameter).replace("'", "''") + "'"; 
/* 354 */     if (parameter instanceof Timestamp)
/* 355 */       return "to_timestamp('" + (new SimpleDateFormat("MM/dd/yyyy HH:mm:ss.SSS"))
/* 356 */         .format(parameter) + "', 'mm/dd/yyyy hh24:mi:ss.ff3')"; 
/* 357 */     if (parameter instanceof Date)
/* 358 */       return "to_date('" + (new SimpleDateFormat("MM/dd/yyyy HH:mm:ss"))
/* 359 */         .format(parameter) + "', 'mm/dd/yyyy hh24:mi:ss')"; 
/* 360 */     if (parameter instanceof Boolean) {
/* 361 */       return ((Boolean)parameter).booleanValue() ? "1" : "0";
/*     */     }
/* 363 */     return parameter.toString();
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\db\FCDBApi.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */