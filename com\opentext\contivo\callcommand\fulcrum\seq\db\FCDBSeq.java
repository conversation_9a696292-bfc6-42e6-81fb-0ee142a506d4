/*     */ package com.opentext.contivo.callcommand.fulcrum.seq.db;
/*     */ 
/*     */ import com.contivo.mixedruntime.RuntimeMessageException;
/*     */ import com.contivo.transform.MessageInfo;
/*     */ import com.contivo.transform.Tag;
/*     */ import com.opentext.contivo.callcommand.fulcrum.seq.FCSeqClient;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.DBPool;
/*     */ import java.sql.Connection;
/*     */ import java.sql.SQLException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FCDBSeq
/*     */   implements FCSeqClient
/*     */ {
/*     */   private final DBPool connectionPool;
/*     */   private final int retries;
/*     */   private final IFCDBSeqApi seqApi;
/*     */   
/*     */   public FCDBSeq(DBPool connectionPool) {
/*  30 */     this.connectionPool = connectionPool;
/*  31 */     this.retries = 3;
/*  32 */     this.seqApi = new FCDBSeqOracleApi();
/*     */   }
/*     */ 
/*     */   
/*     */   public FCDBSeq(DBPool connectionPool, int retries) {
/*  37 */     this.connectionPool = connectionPool;
/*  38 */     this.retries = retries;
/*  39 */     this.seqApi = new FCDBSeqOracleApi();
/*     */   }
/*     */ 
/*     */   
/*     */   public FCDBSeq(DBPool connectionPool, int retries, IFCDBSeqApi seqApi) {
/*  44 */     this.connectionPool = connectionPool;
/*  45 */     this.retries = retries;
/*  46 */     this.seqApi = seqApi;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public long getDbSequenceNumber(long increment, String mainKey, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/*  52 */     for (int i = 0; i < this.retries; i++) {
/*  53 */       Connection conn = this.connectionPool.getConnection(); 
/*  54 */       try { boolean reset = false;
/*     */         
/*  56 */         try { long ret = this.seqApi.incrementSequenceNumberAndGet(conn, increment, mainKey, optionalKey1, optionalKey2, optionalKey3);
/*  57 */           if (ret == 0L) {
/*  58 */             if (conn.getAutoCommit()) {
/*  59 */               reset = true;
/*  60 */               conn.setAutoCommit(false);
/*     */             } 
/*     */             
/*  63 */             ret = this.seqApi.incrementSequenceNumberAndGet(conn, increment, mainKey, optionalKey1, optionalKey2, optionalKey3);
/*  64 */             if (ret == 0L) {
/*  65 */               if (this.seqApi.updateRefNo(conn) == 0) {
/*  66 */                 this.seqApi.insertRefNo(conn);
/*     */               }
/*  68 */               this.seqApi.insertSequenceNumber(conn, increment, mainKey, optionalKey1, optionalKey2, optionalKey3);
/*  69 */               ret = increment;
/*     */             } 
/*     */           } 
/*  72 */           if (!conn.getAutoCommit()) {
/*  73 */             conn.commit();
/*     */           }
/*     */           
/*  76 */           long l1 = ret;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/*  87 */           if (reset)
/*  88 */             conn.setAutoCommit(true);  return l1; } catch (SQLException e) { if (!conn.getAutoCommit()) conn.rollback();  } catch (Throwable e) { if (!conn.getAutoCommit()) conn.rollback();  throw e; } finally { if (reset) conn.setAutoCommit(true);
/*     */            }
/*     */         
/*  91 */         if (conn != null) conn.close();  } catch (Throwable throwable) { if (conn != null)
/*     */           try { conn.close(); } catch (Throwable throwable1) { throwable.addSuppressed(throwable1); }   throw throwable; } 
/*  93 */     }  MessageInfo info = new MessageInfo("FCDBSeq.getDbSequenceNumber", new Tag[] { Tag.FATAL });
/*  94 */     throw new RuntimeMessageException(info, "Exceeded max retries");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setDbSequenceNumber(long seqNum, String mainKey, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/* 100 */     for (int i = 0; i < this.retries; i++) {
/* 101 */       Connection conn = this.connectionPool.getConnection(); try {
/* 102 */         boolean reset = false;
/*     */         try {
/* 104 */           long updateVal = this.seqApi.updateSequenceNumber(conn, seqNum, mainKey, optionalKey1, optionalKey2, optionalKey3);
/* 105 */           if (updateVal == 0L) {
/* 106 */             if (conn.getAutoCommit()) {
/* 107 */               reset = true;
/* 108 */               conn.setAutoCommit(false);
/*     */             } 
/*     */             
/* 111 */             updateVal = this.seqApi.updateSequenceNumber(conn, seqNum, mainKey, optionalKey1, optionalKey2, optionalKey3);
/* 112 */             if (updateVal == 0L) {
/* 113 */               if (this.seqApi.updateRefNo(conn) == 0) {
/* 114 */                 this.seqApi.insertRefNo(conn);
/*     */               }
/* 116 */               this.seqApi.insertSequenceNumber(conn, seqNum, mainKey, optionalKey1, optionalKey2, optionalKey3);
/*     */             } 
/*     */           } 
/* 119 */           if (!conn.getAutoCommit()) {
/* 120 */             conn.commit();
/*     */           }
/* 122 */         } catch (SQLException e) {
/* 123 */           if (!conn.getAutoCommit()) {
/* 124 */             conn.rollback();
/*     */           }
/* 126 */           if (i == this.retries - 1) {
/* 127 */             MessageInfo info = new MessageInfo("FCDBSeq.setDbSequenceNumber", new Tag[] { Tag.FATAL });
/* 128 */             throw new RuntimeMessageException(info, "Exceeded max retries");
/*     */           } 
/* 130 */         } catch (Throwable e) {
/* 131 */           if (!conn.getAutoCommit()) {
/* 132 */             conn.rollback();
/*     */           }
/* 134 */           throw e;
/*     */         } finally {
/* 136 */           if (reset) {
/* 137 */             conn.setAutoCommit(true);
/*     */           }
/*     */         } 
/* 140 */         if (conn != null) conn.close(); 
/*     */       } catch (Throwable throwable) {
/*     */         if (conn != null)
/*     */           try {
/*     */             conn.close();
/*     */           } catch (Throwable throwable1) {
/*     */             throwable.addSuppressed(throwable1);
/*     */           }  
/*     */         throw throwable;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\db\FCDBSeq.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */