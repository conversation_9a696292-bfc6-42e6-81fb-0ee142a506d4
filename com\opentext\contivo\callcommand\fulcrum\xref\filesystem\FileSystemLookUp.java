/*    */ package com.opentext.contivo.callcommand.fulcrum.xref.filesystem;
/*    */ 
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.LookUpClient;
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.Op;
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.Table;
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.cache.table.FCTableCacheKey;
/*    */ import fj.P2;
/*    */ import java.io.File;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FileSystemLookUp
/*    */   implements LookUpClient
/*    */ {
/*    */   private final File baseDirectory;
/* 31 */   private Map<FCTableCacheKey, Table> tables = new HashMap<>();
/*    */ 
/*    */   
/*    */   public FileSystemLookUp(File baseDirectory) {
/* 35 */     this.baseDirectory = Objects.<File>requireNonNull(baseDirectory);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String lookupValue(String solutionID, String tableName, String[] findCols, String resultCol, String[] findValues, Op[] ops, boolean useColName) throws Exception {
/* 46 */     Table table = findAndSetTable(solutionID, tableName);
/* 47 */     return table.findValue(findCols, resultCol, findValues, ops, useColName);
/*    */   }
/*    */ 
/*    */   
/*    */   private Table findAndSetTable(String solutionID, String tableName) throws Exception {
/* 52 */     FCTableCacheKey key = new FCTableCacheKey(solutionID, tableName);
/* 53 */     Table table = this.tables.get(key);
/* 54 */     if (table == null) {
/*    */       
/* 56 */       File tableFile = new File(this.baseDirectory, solutionID + "_" + solutionID + "_hdr.txt");
/* 57 */       boolean hasColHeaders = true;
/* 58 */       if (!tableFile.exists()) {
/* 59 */         hasColHeaders = false;
/* 60 */         tableFile = new File(this.baseDirectory, solutionID + "_" + solutionID + ".txt");
/* 61 */         if (!tableFile.exists()) {
/* 62 */           throw new Exception("Table '" + solutionID + "_" + tableName + "[_hdr].txt' not found in " + this.baseDirectory.getAbsolutePath() + " as specified by fc.fs.path property set in Tools->Options->User Properties Manager");
/*    */         }
/*    */       } 
/*    */       
/* 66 */       P2<Map<String, Integer>, List<Map<Integer, String>>> rowsAsArrays = TableParser.parse(tableFile, hasColHeaders);
/* 67 */       table = new Table(solutionID, tableName, (Map)rowsAsArrays._1(), (List)rowsAsArrays._2(), true);
/* 68 */       this.tables.put(key, table);
/*    */     } 
/* 70 */     return table;
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\filesystem\FileSystemLookUp.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */