/*    */ package com.opentext.contivo.callcommand.fulcrum.xref.db;
/*    */ 
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.LookUpClient;
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.Op;
/*    */ import java.io.IOException;
/*    */ import java.sql.Connection;
/*    */ import java.sql.SQLException;
/*    */ import java.util.Map;
/*    */ import java.util.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCLookUpNoCache
/*    */   implements LookUpClient
/*    */ {
/*    */   private final DBPool connectionPool;
/*    */   
/*    */   public FCLookUpNoCache(DBPool connectionPool) {
/* 23 */     this.connectionPool = Objects.<DBPool>requireNonNull(connectionPool);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String lookupValue(String solutionID, String tableName, String[] findCols, String resultCol, String[] findValues, Op[] ops, boolean useColName) throws Exception {
/* 29 */     Map<String, Integer> headers = getTableHeaders(solutionID, tableName);
/* 30 */     String ret = null;
/* 31 */     if (useColName) {
/* 32 */       String[] convertedFromColName = new String[findCols.length];
/* 33 */       String convertedResColName = null;
/*    */       
/* 35 */       if (headers.isEmpty()) {
/* 36 */         throw new Exception("Table " + tableName + " in " + solutionID + " does not have a header row, but you are attemping to do a lookup by column name. Ensure that the checkbox that says \"Contains Header Row\" is checked when importing through TGTS UI or use the LoadXref service and specify the Header in the policy definition.");
/*    */       }
/*    */ 
/*    */       
/* 40 */       for (int i = 0; i < findCols.length; i++) {
/* 41 */         Integer integer = headers.get(findCols[i]);
/* 42 */         if (integer == null) {
/* 43 */           throw new Exception("Expected a column name for parameter 1 (findCols) index [" + i + 1 + "]. However '" + findCols[i] + "' does not match the name of any columns.");
/*    */         }
/*    */         
/* 46 */         convertedFromColName[i] = integer.toString();
/*    */       } 
/* 48 */       Integer _header = headers.get(resultCol);
/* 49 */       if (_header == null) {
/* 50 */         throw new Exception("Expected a column name for parameter 2 (resultCol). However '" + resultCol + "' does not match the name of any columns.");
/*    */       }
/*    */ 
/*    */       
/* 54 */       convertedResColName = _header.toString();
/*    */       
/* 56 */       ret = getLookupValue(solutionID, tableName, convertedFromColName, convertedResColName, findValues, ops);
/*    */     } else {
/*    */       
/* 59 */       ret = getLookupValue(solutionID, tableName, findCols, resultCol, findValues, ops);
/*    */     } 
/*    */     
/* 62 */     return ret;
/*    */   }
/*    */ 
/*    */   
/*    */   private String getLookupValue(String solutionID, String tableName, String[] findCols, String resultCol, String[] findValues, Op[] ops) throws Exception {
/* 67 */     Connection con = this.connectionPool.getConnection(); 
/* 68 */     try { String str = FCDBApi.selectLookupValue(con, solutionID, tableName, findCols, resultCol, findValues, ops);
/* 69 */       if (con != null) con.close();  return str; } catch (Throwable throwable) { if (con != null)
/*    */         try { con.close(); }
/*    */         catch (Throwable throwable1) { throwable.addSuppressed(throwable1); }
/*    */           throw throwable; }
/* 73 */      } private Map<String, Integer> getTableHeaders(String solutionID, String tableName) throws SQLException, IOException { Connection con = this.connectionPool.getConnection(); try {
/* 74 */       Map<String, Integer> map = FCDBApi.getHeaders(con, solutionID, tableName);
/* 75 */       if (con != null) con.close(); 
/*    */       return map;
/*    */     } catch (Throwable throwable) {
/*    */       if (con != null)
/*    */         try {
/*    */           con.close();
/*    */         } catch (Throwable throwable1) {
/*    */           throwable.addSuppressed(throwable1);
/*    */         }  
/*    */       throw throwable;
/*    */     }  }
/*    */ 
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\db\FCLookUpNoCache.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */