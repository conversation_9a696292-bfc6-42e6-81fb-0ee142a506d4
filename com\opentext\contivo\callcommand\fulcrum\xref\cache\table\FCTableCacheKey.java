/*    */ package com.opentext.contivo.callcommand.fulcrum.xref.cache.table;
/*    */ 
/*    */ import java.util.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCTableCacheKey
/*    */ {
/*    */   private final String solutionID;
/*    */   private final String tableName;
/*    */   private final int hashCode;
/*    */   
/*    */   public FCTableCacheKey(String solutionID, String tableName) {
/* 20 */     this.solutionID = Objects.<String>requireNonNull(solutionID);
/* 21 */     this.tableName = Objects.<String>requireNonNull(tableName);
/* 22 */     this.hashCode = this.solutionID.hashCode() ^ this.tableName.hashCode();
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 27 */     StringBuilder sb = new StringBuilder();
/* 28 */     sb.append("TGTSTableCacheKey:\n");
/* 29 */     sb.append("Solution ID:\t" + this.solutionID + "\n");
/* 30 */     sb.append("Table Name:\t" + this.tableName + "\n");
/* 31 */     return sb.toString();
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object other) {
/* 36 */     if (this == other)
/* 37 */       return true; 
/* 38 */     if (other == null)
/* 39 */       return false; 
/* 40 */     if (!(other instanceof FCTableCacheKey)) {
/* 41 */       return false;
/*    */     }
/* 43 */     FCTableCacheKey otherKey = (FCTableCacheKey)other;
/* 44 */     boolean sameSolutionID = this.solutionID.equals(otherKey.solutionID);
/* 45 */     boolean sameTableName = this.tableName.equals(otherKey.tableName);
/* 46 */     return (sameSolutionID && sameTableName);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 52 */     return this.hashCode;
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\cache\table\FCTableCacheKey.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */