/*    */ package com.liaison.contivo.tle;
/*    */ 
/*    */ import com.contivo.analyst.util.ContivoExternalClass;
/*    */ import com.contivo.analyst.util.ContivoExternalMethod;
/*    */ import com.contivo.runtime.core.IContivoRuntime;
/*    */ import com.contivo.runtime.core.TRuntimeEngineException;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @ContivoExternalClass
/*    */ public class CallCommands
/*    */ {
/*    */   @ContivoExternalMethod(alias = "PadLeft")
/*    */   public String padLeft(IContivoRuntime runtime, String value, String padCharacter, String width) throws TRuntimeEngineException {
/*    */     int w;
/* 22 */     StringBuilder buf = new StringBuilder();
/*    */ 
/*    */     
/*    */     try {
/* 26 */       w = Integer.parseInt(width);
/* 27 */     } catch (NumberFormatException e) {
/*    */       
/* 29 */       throw new TRuntimeEngineException("A field length of '" + width + "' was passed in.  An integer value is required.");
/*    */     } 
/*    */     
/* 32 */     if (padCharacter == null)
/*    */     {
/* 34 */       throw new TRuntimeEngineException("The pad character was null.");
/*    */     }
/* 36 */     if (padCharacter.length() != 1)
/*    */     {
/* 38 */       throw new TRuntimeEngineException("The pad character '" + padCharacter + "' is not 1 character long.  It is " + padCharacter.length() + ".");
/*    */     }
/*    */     
/* 41 */     if (value == null)
/*    */     {
/* 43 */       throw new TRuntimeEngineException("The input value was null.");
/*    */     }
/* 45 */     if (value.length() > w)
/*    */     {
/* 47 */       throw new TRuntimeEngineException("The input value '" + value + "' is longer than the width.");
/*    */     }
/*    */     
/* 50 */     for (; buf.length() + value.length() < w; buf.append(padCharacter));
/* 51 */     buf.append(value);
/*    */     
/* 53 */     return buf.toString();
/*    */   }
/*    */   
/*    */   @ContivoExternalMethod(alias = "PadRight")
/*    */   public String padRight(IContivoRuntime runtime, String value, String padCharacter, String width) throws TRuntimeEngineException {
/*    */     int w;
/* 59 */     StringBuilder buf = new StringBuilder();
/*    */ 
/*    */     
/*    */     try {
/* 63 */       w = Integer.parseInt(width);
/* 64 */     } catch (NumberFormatException e) {
/*    */       
/* 66 */       throw new TRuntimeEngineException("A field length of '" + width + "' was passed in.  An integer value is required.");
/*    */     } 
/*    */     
/* 69 */     if (padCharacter == null)
/*    */     {
/* 71 */       throw new TRuntimeEngineException("The pad character was null.");
/*    */     }
/* 73 */     if (padCharacter.length() != 1)
/*    */     {
/* 75 */       throw new TRuntimeEngineException("The pad character '" + padCharacter + "' is not 1 character long.  It is " + padCharacter.length() + ".");
/*    */     }
/*    */     
/* 78 */     if (value == null)
/*    */     {
/* 80 */       throw new TRuntimeEngineException("The input value was null.");
/*    */     }
/* 82 */     if (value.length() > w)
/*    */     {
/* 84 */       throw new TRuntimeEngineException("The input value '" + value + "' is longer than the width.");
/*    */     }
/*    */     
/* 87 */     buf.append(value); for (; buf.length() < w; buf.append(padCharacter));
/*    */     
/* 89 */     return buf.toString();
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\liaison\contivo\tle\CallCommands.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */