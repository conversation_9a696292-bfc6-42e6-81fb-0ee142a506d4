/*     */ package com.opentext.contivo.callcommand.fulcrum.seq;
/*     */ 
/*     */ import com.contivo.analyst.util.ContivoExternalClass;
/*     */ import com.contivo.analyst.util.ContivoExternalMethod;
/*     */ import com.contivo.mixedruntime.RuntimeMessageException;
/*     */ import com.contivo.runtime.core.IContivoRuntime;
/*     */ import com.contivo.runtime.core.IContivoRuntimeSelfTimeout;
/*     */ import com.contivo.transform.MessageInfo;
/*     */ import com.contivo.transform.Tag;
/*     */ import com.opentext.contivo.callcommand.fulcrum.EnvProp;
/*     */ import com.opentext.contivo.callcommand.fulcrum.EnvironmentQuery;
/*     */ import com.opentext.contivo.callcommand.fulcrum.IPerformanceMetrics;
/*     */ import com.opentext.contivo.callcommand.fulcrum.seq.db.FCDBSeq;
/*     */ import com.opentext.contivo.callcommand.fulcrum.seq.local.FCLocalSeq;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.DBPool;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.SimpleConnectionToDBPool;
/*     */ import java.util.Optional;
/*     */ import java.util.Properties;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @ContivoExternalClass
/*     */ public class FCSeqCallCmd
/*     */   implements IContivoRuntimeSelfTimeout
/*     */ {
/*     */   private FCSeqProp props;
/*     */   private FCSeqClient client;
/*     */   private boolean hasUpdated = false;
/*     */   private IPerformanceMetrics metrics;
/*     */   
/*     */   private void init(IContivoRuntime icr) throws RuntimeMessageException {
/*  48 */     if (this.client != null) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/*  53 */     MessageInfo info = new MessageInfo("FCSeqCallCmd.init", new Tag[] { Tag.FATAL });
/*     */ 
/*     */     
/*  56 */     this.client = (FCSeqClient)icr.getInstance(FCSeqClient.class);
/*  57 */     this.props = (FCSeqProp)icr.getInstance(FCSeqProp.class);
/*  58 */     this.metrics = (IPerformanceMetrics)icr.getInstance(IPerformanceMetrics.class);
/*  59 */     Properties envJavaProp = EnvironmentQuery.getEnvJavaProp(icr);
/*  60 */     EnvProp envProp = EnvironmentQuery.getEnvProp(icr, envJavaProp);
/*     */ 
/*     */     
/*  63 */     Properties prop = icr.getUserProperties();
/*     */     
/*  65 */     if (this.props == null)
/*     */     {
/*  67 */       if (envProp != null) {
/*  68 */         this.props = new FCSeqProp(envProp.getSolutionID(), envProp.isTraceMode());
/*     */       } else {
/*     */         
/*  71 */         String solutionID = prop.getProperty("fc.solutionID");
/*  72 */         this.props = new FCSeqProp(solutionID);
/*     */       } 
/*     */     }
/*     */     
/*  76 */     if (this.client == null)
/*     */     {
/*  78 */       if (prop.getProperty("fc.db.url") == null) {
/*     */         
/*  80 */         this.client = (FCSeqClient)new FCLocalSeq();
/*     */       }
/*     */       else {
/*     */         
/*  84 */         String dbURL = (String)Optional.<String>ofNullable(prop.getProperty("fc.db.url")).orElseThrow(() -> new RuntimeMessageException(info, "Please specify the fc.db.url in Tools->Options->User Properties Manager"));
/*     */         
/*  86 */         String driverName = (String)Optional.<String>ofNullable(prop.getProperty("fc.db.driver")).orElseThrow(() -> new RuntimeMessageException(info, "Please specify the fc.db.driver in Tools->Options->User Properties Manager"));
/*     */         
/*  88 */         String userName = prop.getProperty("fc.db.user");
/*  89 */         String password = prop.getProperty("fc.db.password");
/*  90 */         SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(dbURL, driverName, userName, password);
/*  91 */         this.client = (FCSeqClient)new FCDBSeq((DBPool)dbPool, 3);
/*     */       } 
/*     */     }
/*     */     
/*  95 */     if (this.client == null || this.props == null)
/*     */     {
/*  97 */       throw new RuntimeMessageException(info, "Failed to initialize");
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbSequenceNumber")
/*     */   public String getDbSequenceNumber(IContivoRuntime icr, String increment, String mainKey) throws RuntimeMessageException {
/* 112 */     return getDbSequenceNumber(icr, increment, mainKey, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbSequenceNumber")
/*     */   public String getDbSequenceNumber(IContivoRuntime icr, String increment, String mainKey, String optionalKey1) throws RuntimeMessageException {
/* 128 */     return getDbSequenceNumber(icr, increment, mainKey, optionalKey1, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbSequenceNumber")
/*     */   public String getDbSequenceNumber(IContivoRuntime icr, String increment, String mainKey, String optionalKey1, String optionalKey2) throws RuntimeMessageException {
/* 145 */     long startTime = System.currentTimeMillis();
/*     */     
/*     */     try {
/* 148 */       init(icr);
/* 149 */       long ret = 0L;
/* 150 */       if (this.props.getSolutionID() != null) {
/*     */         
/* 152 */         ret = this.client.getDbSequenceNumber(parseLong(increment, "increment").longValue(), this.props.getSolutionID(), mainKey, optionalKey1, optionalKey2);
/*     */       
/*     */       }
/*     */       else {
/*     */         
/* 157 */         ret = this.client.getDbSequenceNumber(parseLong(increment, "increment").longValue(), mainKey, optionalKey1, optionalKey2, null);
/*     */       } 
/*     */ 
/*     */       
/* 161 */       if (this.props.isTraceMode())
/*     */       {
/* 163 */         icr.addTraceMessage("FCSeq.getDbSequenceNumber", "{\"solutionId\":\"" + this.props
/* 164 */             .getSolutionID() + "\", \"increment\":\"" + increment + "\", \"key1\":\"" + mainKey + "\", \"key2\":\"" + optionalKey1 + "\", \"key3\":\"" + optionalKey2 + "\"} = " + ret);
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 169 */       return "" + ret;
/*     */     
/*     */     }
/* 172 */     catch (RuntimeMessageException e) {
/*     */       
/* 174 */       throw e;
/*     */     }
/* 176 */     catch (Exception e) {
/*     */       
/* 178 */       MessageInfo info = new MessageInfo("FCSeq.getDbSequenceNumber", new Tag[] { Tag.FATAL });
/* 179 */       throw new RuntimeMessageException(e, info, e.getMessage());
/*     */     } finally {
/* 181 */       if (this.metrics != null) {
/* 182 */         this.metrics.incre("FCSeq.getDbSeqNum", 1L);
/* 183 */         this.metrics.incre("FCSeq.getDbSeqNumTime", System.currentTimeMillis() - startTime);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "setDbSequenceNumber")
/*     */   public String setDbSequenceNumber(IContivoRuntime icr, String seqNum, String mainKey) throws RuntimeMessageException {
/* 199 */     return setDbSequenceNumber(icr, seqNum, mainKey, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "setDbSequenceNumber")
/*     */   public String setDbSequenceNumber(IContivoRuntime icr, String seqNum, String mainKey, String optionalKey1) throws RuntimeMessageException {
/* 215 */     return setDbSequenceNumber(icr, seqNum, mainKey, optionalKey1, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "setDbSequenceNumber")
/*     */   public String setDbSequenceNumber(IContivoRuntime icr, String seqNum, String mainKey, String optionalKey1, String optionalKey2) throws RuntimeMessageException {
/* 232 */     long startTime = System.currentTimeMillis();
/*     */     
/*     */     try {
/* 235 */       init(icr);
/* 236 */       if (this.props.getSolutionID() != null) {
/*     */         
/* 238 */         this.client.setDbSequenceNumber(parseLong(seqNum, "seqNum").longValue(), this.props.getSolutionID(), mainKey, optionalKey1, optionalKey2);
/*     */       
/*     */       }
/*     */       else {
/*     */         
/* 243 */         this.client.setDbSequenceNumber(parseLong(seqNum, "seqNum").longValue(), mainKey, optionalKey1, optionalKey2, null);
/*     */       } 
/*     */       
/* 246 */       if (this.props.isTraceMode())
/*     */       {
/* 248 */         icr.addTraceMessage("FCSeq.setDbSequenceNumber", "{\"seqNum\":\"" + seqNum + "\", \"solutionId\":\"" + this.props
/* 249 */             .getSolutionID() + "\", \"key1\":\"" + mainKey + "\", \"key2\":\"" + optionalKey1 + "\", \"key3\":\"" + optionalKey2 + "\"}");
/*     */       }
/*     */ 
/*     */       
/* 253 */       return "";
/*     */     }
/* 255 */     catch (RuntimeMessageException e) {
/*     */       
/* 257 */       throw e;
/*     */     }
/* 259 */     catch (Exception e) {
/*     */       
/* 261 */       MessageInfo info = new MessageInfo("FCSeq.setDbSequenceNumber", new Tag[] { Tag.FATAL });
/* 262 */       throw new RuntimeMessageException(e, info, e.getMessage());
/*     */     } finally {
/* 264 */       if (this.metrics != null) {
/* 265 */         this.metrics.incre("FCSeq.setDbSeqNum", 1L);
/* 266 */         this.metrics.incre("FCSeq.setDbSeqNumTime", System.currentTimeMillis() - startTime);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbSessionSequenceNumber")
/*     */   public String getDbSessionSequenceNumber(IContivoRuntime icr, String increment, String mainKey) throws RuntimeMessageException {
/* 282 */     return getDbSessionSequenceNumber(icr, increment, mainKey, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbSessionSequenceNumber")
/*     */   public String getDbSessionSequenceNumber(IContivoRuntime icr, String increment, String mainKey, String optionalKey1) throws RuntimeMessageException {
/* 298 */     return getDbSessionSequenceNumber(icr, increment, mainKey, optionalKey1, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbSessionSequenceNumber")
/*     */   public String getDbSessionSequenceNumber(IContivoRuntime icr, String increment, String mainKey, String optionalKey1, String optionalKey2) throws RuntimeMessageException {
/* 315 */     init(icr);
/*     */     
/* 317 */     String incre = increment;
/* 318 */     synchronized (this) {
/*     */       
/* 320 */       if (!this.hasUpdated) {
/*     */         
/* 322 */         this.hasUpdated = true;
/*     */       }
/*     */       else {
/*     */         
/* 326 */         incre = "0";
/*     */       } 
/*     */     } 
/* 329 */     return getDbSequenceNumber(icr, incre, mainKey, optionalKey1, optionalKey2);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static Long parseLong(String stringToParse, String name) throws RuntimeMessageException {
/*     */     try {
/* 336 */       return Long.valueOf(Long.parseLong(stringToParse));
/*     */     }
/* 338 */     catch (NumberFormatException e) {
/*     */       
/* 340 */       MessageInfo info = new MessageInfo("FCSeq.parseLong", new Tag[] { Tag.FATAL });
/* 341 */       throw new RuntimeMessageException(e, info, name + " is not a integer");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\FCSeqCallCmd.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */