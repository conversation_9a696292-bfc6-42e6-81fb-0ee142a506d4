package com.opentext.contivo.callcommand.fulcrum.seq.db;

import java.sql.Connection;
import java.sql.SQLException;

public interface IFCDBSeqApi {
  long getSequenceNumber(Connection paramConnection, String paramString1, String paramString2, String paramString3, String paramString4) throws Exception;
  
  long incrementSequenceNumberAndGet(Connection paramConnection, long paramLong, String paramString1, String paramString2, String paramString3, String paramString4) throws Exception;
  
  int updateSequenceNumber(Connection paramConnection, long paramLong, String paramString1, String paramString2, String paramString3, String paramString4) throws Exception;
  
  boolean insertSequenceNumber(Connection paramConnection, long paramLong, String paramString1, String paramString2, String paramString3, String paramString4) throws Exception;
  
  int updateRefNo(Connection paramConnection) throws SQLException;
  
  void insertRefNo(Connection paramConnection) throws SQLException;
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\db\IFCDBSeqApi.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */