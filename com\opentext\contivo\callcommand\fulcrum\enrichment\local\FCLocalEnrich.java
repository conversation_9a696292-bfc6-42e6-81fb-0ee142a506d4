/*    */ package com.opentext.contivo.callcommand.fulcrum.enrichment.local;
/*    */ 
/*    */ import com.opentext.contivo.callcommand.fulcrum.enrichment.IDbEnrichClient;
/*    */ import java.io.File;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCLocalEnrich
/*    */   implements IDbEnrichClient
/*    */ {
/*    */   private final File baseDirectory;
/* 21 */   private static final Map<String, Map<EnrichKey, String>> enrichMap = new HashMap<>();
/*    */   
/*    */   public FCLocalEnrich(File baseDirectory) {
/* 24 */     this.baseDirectory = baseDirectory;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean saveDbData(String solutionId, int dataItemNumber, String key1, String key2, String key3, String key4, String value, int duplicateOption, int daysToExpire) throws Exception {
/* 31 */     if (dataItemNumber > 4 || dataItemNumber < 1) {
/* 32 */       throw new Exception("Invalid data selection");
/*    */     }
/*    */     
/* 35 */     if (duplicateOption > 3 || duplicateOption < 1) {
/* 36 */       throw new Exception("Invalid duplicateOption: " + duplicateOption);
/*    */     }
/* 38 */     Map<EnrichKey, String> enrichValues = null;
/* 39 */     if (enrichMap.get(solutionId) != null) {
/* 40 */       enrichValues = enrichMap.get(solutionId);
/*    */     } else {
/*    */       
/* 43 */       enrichValues = EnrichTableParser.parse(new File(this.baseDirectory, solutionId + ".txt"));
/* 44 */       enrichMap.put(solutionId, enrichValues);
/*    */     } 
/* 46 */     EnrichKey key = new EnrichKey(key1, key2, key3, key4, "" + dataItemNumber);
/* 47 */     if (duplicateOption == 1) {
/*    */       
/* 49 */       enrichValues.put(key, value);
/*    */     }
/* 51 */     else if (duplicateOption != 2) {
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 57 */       if (enrichValues.containsKey(key))
/*    */       {
/* 59 */         throw new Exception("Duplicate record found!");
/*    */       }
/*    */ 
/*    */       
/* 63 */       enrichValues.put(key, value);
/*    */     } 
/*    */ 
/*    */     
/* 67 */     EnrichTableParser.persist(new File(this.baseDirectory, solutionId + ".txt"), enrichValues);
/* 68 */     return true;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getDbData(String solutionId, int dataItemNumber, String key1, String key2, String key3, String key4, int resultFlag) throws Exception {
/* 75 */     if (dataItemNumber > 4 || dataItemNumber < 1) {
/* 76 */       throw new Exception("Invalid data selection");
/*    */     }
/*    */     
/* 79 */     if (resultFlag != 1 && resultFlag != 2) {
/* 80 */       throw new Exception("Invalid resultFlag: " + resultFlag);
/*    */     }
/* 82 */     Map<EnrichKey, String> enrichValues = null;
/* 83 */     if (enrichMap.get(solutionId) != null) {
/* 84 */       enrichValues = enrichMap.get(solutionId);
/*    */     } else {
/* 86 */       enrichValues = EnrichTableParser.parse(new File(this.baseDirectory, solutionId + ".txt"));
/* 87 */       enrichMap.put(solutionId, enrichValues);
/*    */     } 
/* 89 */     EnrichKey key = new EnrichKey(key1, key2, key3, key4, "" + dataItemNumber);
/* 90 */     String ret = enrichValues.get(key);
/* 91 */     if (ret == null && resultFlag == 2) {
/* 92 */       throw new Exception("Record not Found!");
/*    */     }
/*    */     
/* 95 */     return ret;
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\enrichment\local\FCLocalEnrich.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */