/*     */ package com.opentext.contivo.callcommand.fulcrum.enrichment.db;
/*     */ 
/*     */ import com.opentext.contivo.callcommand.fulcrum.db.RefNoApi;
/*     */ import java.sql.Connection;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.util.Date;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FCDBEnrichApi
/*     */ {
/*     */   private static final String TABLE_NAME_DOCS = "EC_ENRICHMENT_DATA";
/*     */   private static final String COL_KEY1 = "KEY1";
/*     */   private static final String COL_KEY2 = "KEY2";
/*     */   private static final String COL_KEY3 = "KEY3";
/*     */   private static final String COL_KEY4 = "KEY4";
/*     */   private static final String COL_DATA1 = "DATA1";
/*     */   private static final String COL_DATA2 = "DATA2";
/*     */   private static final String COL_DATA3 = "DATA3";
/*     */   private static final String COL_DATA4 = "DATA4";
/*     */   private static final String COL_INSERTION_TIME = "INSTRD_DATE_TIME";
/*     */   private static final String COL_REF_NO = "REF_NO";
/*     */   private static final String COL_SOLUTION_ID = "SOLUTION_ID";
/*     */   private static final String COL_EXPIRE_TIME = "EXPIRE_DATE_TIME";
/*  39 */   private static final Long EXPIRE_DAY_IN_SECONDS = Long.valueOf(86400000L);
/*     */ 
/*     */   
/*     */   private static final String ENRICHMENT_REFNO_NAME = "enrichment_ref_no";
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getDbData(Connection conn, String solutionId, int dataItemNumber, String key1, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/*  47 */     if (solutionId == null)
/*     */     {
/*  49 */       throw new Exception("SolutionId cannot be null");
/*     */     }
/*  51 */     if (key1 == null)
/*     */     {
/*  53 */       throw new Exception("First key must not be null");
/*     */     }
/*  55 */     String select = "";
/*  56 */     switch (dataItemNumber) { case 1:
/*  57 */         select = "DATA1"; break;
/*  58 */       case 2: select = "DATA2"; break;
/*  59 */       case 3: select = "DATA3"; break;
/*  60 */       case 4: select = "DATA4"; break;
/*  61 */       default: throw new Exception("Invalid data selection"); }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  69 */     String sqlStmt = "SELECT " + select + ", REF_NO FROM  EC_ENRICHMENT_DATA WHERE SOLUTION_ID = ? AND KEY1 = ? AND KEY2" + ((optionalKey1 != null) ? " = ? " : " IS NULL") + " AND KEY3" + ((optionalKey2 != null) ? " = ? " : " IS NULL") + " AND KEY4" + ((optionalKey3 != null) ? " = ? " : " IS NULL");
/*     */     
/*  71 */     PreparedStatement pStmt = null;
/*  72 */     ResultSet rset = null;
/*     */     
/*     */     try {
/*  75 */       pStmt = conn.prepareStatement(sqlStmt, 1004, 1007);
/*     */       
/*  77 */       int i = 1;
/*  78 */       pStmt.setString(i++, solutionId);
/*  79 */       pStmt.setString(i++, key1);
/*     */       
/*  81 */       if (optionalKey1 != null) {
/*  82 */         pStmt.setString(i++, optionalKey1);
/*     */       }
/*  84 */       if (optionalKey2 != null) {
/*  85 */         pStmt.setString(i++, optionalKey2);
/*     */       }
/*  87 */       if (optionalKey3 != null) {
/*  88 */         pStmt.setString(i++, optionalKey3);
/*     */       }
/*  90 */       pStmt.setFetchSize(1);
/*  91 */       rset = pStmt.executeQuery();
/*     */       
/*  93 */       if (rset.next())
/*     */       {
/*  95 */         return rset.getString(1);
/*     */       }
/*     */     }
/*     */     finally {
/*     */       
/* 100 */       if (rset != null)
/*     */       {
/* 102 */         rset.close();
/*     */       }
/* 104 */       if (pStmt != null)
/*     */       {
/* 106 */         pStmt.close();
/*     */       }
/*     */     } 
/* 109 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static int updateDbData(Connection conn, String solutionId, int dataItemNumber, int daysToExpire, String key1, String optionalKey1, String optionalKey2, String optionalKey3, String data) throws Exception {
/* 115 */     if (solutionId == null)
/*     */     {
/* 117 */       throw new Exception("SolutionId cannot be null");
/*     */     }
/* 119 */     if (key1 == null)
/*     */     {
/* 121 */       throw new Exception("First key must not be null");
/*     */     }
/* 123 */     String dataCol = "";
/* 124 */     switch (dataItemNumber) { case 1:
/* 125 */         dataCol = "DATA1"; break;
/* 126 */       case 2: dataCol = "DATA2"; break;
/* 127 */       case 3: dataCol = "DATA3"; break;
/* 128 */       case 4: dataCol = "DATA4"; break;
/* 129 */       default: throw new Exception("Invalid data selection"); }
/*     */     
/* 131 */     StringBuilder builder = new StringBuilder();
/* 132 */     builder.append("UPDATE EC_ENRICHMENT_DATA SET  " + dataCol + " = ?, INSTRD_DATE_TIME = ? ");
/* 133 */     if (daysToExpire > 0) {
/* 134 */       builder.append(", EXPIRE_DATE_TIME = ? ");
/*     */     }
/*     */     
/* 137 */     builder.append(" WHERE SOLUTION_ID = ?  AND KEY1 = ?  AND KEY2" + (
/*     */ 
/*     */         
/* 140 */         (optionalKey1 != null) ? " = ? " : " IS NULL") + " AND KEY3" + (
/* 141 */         (optionalKey2 != null) ? " = ? " : " IS NULL") + " AND KEY4" + (
/* 142 */         (optionalKey3 != null) ? " = ? " : " IS NULL"));
/* 143 */     PreparedStatement pStmt = null;
/* 144 */     int result = 0;
/* 145 */     long curTime = (new Date()).getTime();
/*     */     
/*     */     try {
/* 148 */       pStmt = conn.prepareStatement(builder.toString(), 1004, 1007);
/*     */       
/* 150 */       int i = 1;
/* 151 */       pStmt.setString(i++, data);
/* 152 */       pStmt.setLong(i++, curTime);
/* 153 */       if (daysToExpire > 0) {
/* 154 */         pStmt.setLong(i++, curTime + EXPIRE_DAY_IN_SECONDS.longValue() * daysToExpire);
/*     */       }
/* 156 */       pStmt.setString(i++, solutionId);
/* 157 */       pStmt.setString(i++, key1);
/*     */       
/* 159 */       if (optionalKey1 != null) {
/* 160 */         pStmt.setString(i++, optionalKey1);
/*     */       }
/* 162 */       if (optionalKey2 != null) {
/* 163 */         pStmt.setString(i++, optionalKey2);
/*     */       }
/* 165 */       if (optionalKey3 != null) {
/* 166 */         pStmt.setString(i++, optionalKey3);
/*     */       }
/* 168 */       result = pStmt.executeUpdate();
/*     */     }
/*     */     finally {
/*     */       
/* 172 */       if (pStmt != null)
/*     */       {
/* 174 */         pStmt.close();
/*     */       }
/*     */     } 
/* 177 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean saveDbData(Connection conn, String solutionId, int dataItemNumber, int daysToExpire, String key1, String optionalKey1, String optionalKey2, String optionalKey3, String data) throws Exception {
/* 183 */     if (solutionId == null)
/*     */     {
/* 185 */       throw new Exception("SolutionId cannot be null");
/*     */     }
/* 187 */     if (key1 == null)
/*     */     {
/* 189 */       throw new Exception("First key must not be null");
/*     */     }
/* 191 */     String dataCol = "";
/* 192 */     switch (dataItemNumber) { case 1:
/* 193 */         dataCol = "DATA1"; break;
/* 194 */       case 2: dataCol = "DATA2"; break;
/* 195 */       case 3: dataCol = "DATA3"; break;
/* 196 */       case 4: dataCol = "DATA4"; break;
/* 197 */       default: throw new Exception("Invalid data selection"); }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 211 */     String sqlStmt = "INSERT INTO EC_ENRICHMENT_DATA ( SOLUTION_ID, KEY1, " + ((optionalKey1 != null) ? "KEY2, " : "") + ((optionalKey2 != null) ? "KEY3, " : "") + ((optionalKey3 != null) ? "KEY4, " : "") + dataCol + ", INSTRD_DATE_TIME, EXPIRE_DATE_TIME, REF_NO ) SELECT ?, ?, " + ((optionalKey1 != null) ? "?, " : "") + ((optionalKey2 != null) ? "?, " : "") + ((optionalKey3 != null) ? "?, " : "") + "?, ?, ?, ec_ref_no.\"VALUE\" FROM ec_ref_no WHERE ec_ref_no.name = 'enrichment_ref_no'";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 216 */     PreparedStatement pStmt = null;
/* 217 */     long curTime = (new Date()).getTime();
/*     */ 
/*     */     
/*     */     try {
/* 221 */       pStmt = conn.prepareStatement(sqlStmt, 1004, 1007);
/*     */       
/* 223 */       int i = 1;
/* 224 */       pStmt.setString(i++, solutionId);
/* 225 */       pStmt.setString(i++, key1);
/*     */       
/* 227 */       if (optionalKey1 != null) {
/* 228 */         pStmt.setString(i++, optionalKey1);
/*     */       }
/* 230 */       if (optionalKey2 != null) {
/* 231 */         pStmt.setString(i++, optionalKey2);
/*     */       }
/* 233 */       if (optionalKey3 != null) {
/* 234 */         pStmt.setString(i++, optionalKey3);
/*     */       }
/* 236 */       pStmt.setString(i++, data);
/* 237 */       pStmt.setLong(i++, curTime);
/* 238 */       if (daysToExpire > 0) {
/* 239 */         pStmt.setLong(i++, curTime + EXPIRE_DAY_IN_SECONDS.longValue() * daysToExpire);
/*     */       } else {
/* 241 */         pStmt.setLong(i++, 0L);
/*     */       } 
/* 243 */       return pStmt.execute();
/*     */     }
/*     */     finally {
/*     */       
/* 247 */       if (pStmt != null)
/*     */       {
/* 249 */         pStmt.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static int updateRefNo(Connection conn) throws SQLException {
/* 256 */     return RefNoApi.updateRefNo(conn, "enrichment_ref_no");
/*     */   }
/*     */ 
/*     */   
/*     */   public static void insertRefNo(Connection conn) throws SQLException {
/* 261 */     RefNoApi.insertRefNo(conn, "enrichment_ref_no");
/*     */   }
/*     */ 
/*     */   
/*     */   public static long selectRefNo(Connection conn) throws Exception {
/* 266 */     return RefNoApi.selectRefNo(conn, "enrichment_ref_no");
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\enrichment\db\FCDBEnrichApi.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */