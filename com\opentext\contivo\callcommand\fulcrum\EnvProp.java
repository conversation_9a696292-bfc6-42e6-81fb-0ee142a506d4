/*    */ package com.opentext.contivo.callcommand.fulcrum;
/*    */ 
/*    */ import java.util.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EnvProp
/*    */ {
/*    */   private final String solutionID;
/*    */   private final String senderID;
/*    */   private final String receiverID;
/*    */   private final String docType;
/*    */   private final String snrf;
/*    */   private final boolean traceMode;
/*    */   
/*    */   public EnvProp(String solutionID, String senderID, String receiverID, String docType, String snrf) {
/* 20 */     this.solutionID = Objects.<String>requireNonNull(solutionID);
/* 21 */     this.senderID = Objects.<String>requireNonNull(senderID);
/* 22 */     this.receiverID = Objects.<String>requireNonNull(receiverID);
/* 23 */     this.docType = Objects.<String>requireNonNull(docType);
/* 24 */     this.snrf = Objects.<String>requireNonNull(snrf);
/* 25 */     this.traceMode = false;
/*    */   }
/*    */ 
/*    */   
/*    */   public EnvProp(String solutionID, String senderID, String receiverID, String docType, String snrf, boolean traceMode) {
/* 30 */     this.solutionID = Objects.<String>requireNonNull(solutionID);
/* 31 */     this.senderID = Objects.<String>requireNonNull(senderID);
/* 32 */     this.receiverID = Objects.<String>requireNonNull(receiverID);
/* 33 */     this.docType = Objects.<String>requireNonNull(docType);
/* 34 */     this.snrf = Objects.<String>requireNonNull(snrf);
/* 35 */     this.traceMode = traceMode;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getSolutionID() {
/* 40 */     return this.solutionID;
/*    */   }
/*    */   
/*    */   public String getSenderID() {
/* 44 */     return this.senderID;
/*    */   }
/*    */   
/*    */   public String getReceiverID() {
/* 48 */     return this.receiverID;
/*    */   }
/*    */   
/*    */   public String getDocType() {
/* 52 */     return this.docType;
/*    */   }
/*    */   
/*    */   public String getSNRF() {
/* 56 */     return this.snrf;
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean isTraceMode() {
/* 61 */     return this.traceMode;
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\EnvProp.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */