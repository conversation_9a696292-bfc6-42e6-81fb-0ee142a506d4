/*     */ package com.opentext.contivo.callcommand.fulcrum.xref;
/*     */ 
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Objects;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Table
/*     */ {
/*     */   private final List<Map<Integer, String>> rows;
/*     */   private final Map<String, Integer> nameToNumber;
/*     */   private final boolean isLocal;
/*     */   private final String solutionID;
/*     */   private final String tableName;
/*     */   
/*     */   public Table(String solutionID, String tableName, Map<String, Integer> nameToNumber, List<Map<Integer, String>> rows) {
/*  30 */     this(solutionID, tableName, nameToNumber, rows, true);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Table(String solutionID, String tableName, Map<String, Integer> nameToNumber, List<Map<Integer, String>> rows, boolean isLocal) {
/*  42 */     this.solutionID = solutionID;
/*  43 */     this.tableName = tableName;
/*  44 */     this.rows = Objects.<List<Map<Integer, String>>>requireNonNull(rows);
/*  45 */     this.nameToNumber = nameToNumber;
/*  46 */     this.isLocal = isLocal;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String findValue(String[] findCols, String resultCol, String[] findValue, Op[] ops, boolean useColName) throws Exception {
/*  62 */     Integer resultColNum = getColNum(useColName, resultCol, "2 (resultCol)");
/*     */     
/*  64 */     for (int i = 0; i < this.rows.size(); i++) {
/*     */       
/*  66 */       Map<Integer, String> row = this.rows.get(i);
/*  67 */       boolean hasCols = row.containsKey(getColNum(useColName, findCols[0], "1 (findCols) index [1]"));
/*  68 */       for (int z = 1; z < findCols.length; z++) {
/*  69 */         hasCols &= row.containsKey(getColNum(useColName, findCols[z], "1 (findCols) index [" + z + 1 + "]"));
/*     */       }
/*  71 */       if (hasCols) {
/*     */         
/*  73 */         String[] compareValues = new String[findCols.length];
/*  74 */         String compareValue = row.get(getColNum(useColName, findCols[0], "1 (findCols) index [1]"));
/*  75 */         boolean matched = compare(ops[0], compareValue, findValue[0]);
/*  76 */         for (int j = 1; j < compareValues.length; j++) {
/*  77 */           compareValue = row.get(getColNum(useColName, findCols[j], "1 (findCols) index [" + j + 1 + "]"));
/*  78 */           matched &= compare(ops[j], compareValue, findValue[j]);
/*     */         } 
/*  80 */         if (matched)
/*     */         {
/*  82 */           return row.get(resultColNum);
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/*  87 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getRowSize() {
/*  97 */     return this.rows.size();
/*     */   }
/*     */   
/*     */   public Map<String, Integer> getHeaders() {
/* 101 */     return this.nameToNumber;
/*     */   }
/*     */   
/*     */   private boolean compare(Op op, String value1, String value2) throws Exception {
/* 105 */     switch (op) {
/*     */       case EQ:
/* 107 */         return Objects.equals(value1, value2);
/*     */       case NOT_EQ:
/* 109 */         return !Objects.equals(value1, value2);
/*     */       case CI:
/* 111 */         return (value1 != null) ? value1.equalsIgnoreCase(value2) : ((value2 == null));
/*     */     } 
/* 113 */     throw new Exception("Unsupported Operation");
/*     */   }
/*     */   
/*     */   private Integer getColNum(boolean useColName, String findCol, String debugString) throws Exception {
/* 117 */     Integer colNum = null;
/* 118 */     if (useColName) {
/* 119 */       if (this.nameToNumber.isEmpty()) {
/*     */ 
/*     */         
/* 122 */         String forLocal = this.isLocal ? "Ensure that your table includes a column name header row and the table file name ends in '_hdr.txt'." : "Ensure that the checkbox that says \"Contains Header Row\" is checked when importing through TGTS UI or use the LoadXref service and specify the Header in the policy definition.";
/* 123 */         throw new Exception("Table " + this.tableName + " in " + this.solutionID + " does not have a header row, but you are attemping to do a lookup by column name. " + forLocal);
/*     */       } 
/* 125 */       colNum = this.nameToNumber.get(findCol);
/* 126 */       if (colNum == null) {
/* 127 */         throw new Exception("Expected a column name for parameter " + debugString + ". However '" + findCol + "' does not match the name of any columns.");
/*     */       }
/*     */     } else {
/* 130 */       colNum = parseInt(findCol, debugString);
/*     */     } 
/* 132 */     return colNum;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static Integer parseInt(String stringToParse, String name) throws Exception {
/*     */     try {
/* 139 */       return Integer.valueOf(Integer.parseInt(stringToParse));
/*     */     }
/* 141 */     catch (NumberFormatException e) {
/*     */       
/* 143 */       throw new Exception("Expected a column number (such as '1') for parameter  " + name + ", but found '" + stringToParse + "' instead.");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\Table.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */