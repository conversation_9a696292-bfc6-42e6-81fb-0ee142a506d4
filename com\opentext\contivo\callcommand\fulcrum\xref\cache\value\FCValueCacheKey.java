/*    */ package com.opentext.contivo.callcommand.fulcrum.xref.cache.value;
/*    */ 
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.Op;
/*    */ import com.opentext.contivo.callcommand.fulcrum.xref.cache.table.FCTableCacheKey;
/*    */ import java.util.Arrays;
/*    */ import java.util.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCValueCacheKey
/*    */ {
/*    */   private final FCTableCacheKey tableKey;
/*    */   private final String[] findCols;
/*    */   private final String resultCol;
/*    */   private final String[] findValues;
/*    */   private final Op[] ops;
/*    */   private final int hashCode;
/*    */   
/*    */   public FCValueCacheKey(FCTableCacheKey tableKey, String[] findCols, String resultCol, String[] findValues, Op[] ops) {
/* 27 */     this.tableKey = Objects.<FCTableCacheKey>requireNonNull(tableKey);
/* 28 */     this.findCols = findCols;
/* 29 */     this.resultCol = resultCol;
/* 30 */     this.findValues = findValues;
/* 31 */     this.ops = ops;
/* 32 */     this.hashCode = this.tableKey.hashCode() ^ Arrays.hashCode((Object[])this.findCols) ^ this.resultCol.hashCode() ^ Arrays.hashCode((Object[])findValues) ^ Arrays.hashCode((Object[])ops);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String toString() {
/* 38 */     StringBuilder sb = new StringBuilder();
/* 39 */     sb.append("TGTSValueCacheKey:\n");
/* 40 */     sb.append("TGTSTableCacheKey:\t" + this.tableKey + "\n");
/* 41 */     sb.append("FindCols:\t" + Arrays.toString((Object[])this.findCols) + "\n");
/* 42 */     sb.append("ResultCol:\t" + this.resultCol + "\n");
/* 43 */     sb.append("FindValues:\t" + Arrays.toString((Object[])this.findValues) + "\n");
/* 44 */     sb.append("Ops:\t" + Arrays.toString((Object[])this.ops) + "\n");
/* 45 */     return sb.toString();
/*    */   }
/*    */ 
/*    */   
/*    */   public boolean equals(Object other) {
/* 50 */     if (this == other)
/* 51 */       return true; 
/* 52 */     if (other == null)
/* 53 */       return false; 
/* 54 */     if (!(other instanceof FCValueCacheKey)) {
/* 55 */       return false;
/*    */     }
/* 57 */     FCValueCacheKey otherKey = (FCValueCacheKey)other;
/* 58 */     boolean sameTableKey = this.tableKey.equals(otherKey.tableKey);
/* 59 */     boolean sameFindCol = Arrays.equals((Object[])this.findCols, (Object[])otherKey.findCols);
/* 60 */     boolean sameResultCol = (this.resultCol == otherKey.resultCol);
/* 61 */     boolean sameFindValue = Arrays.equals((Object[])this.findValues, (Object[])otherKey.findValues);
/* 62 */     boolean sameOps = Arrays.equals((Object[])this.ops, (Object[])otherKey.ops);
/* 63 */     return (sameTableKey && sameFindCol && sameResultCol && sameFindValue && sameOps);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 69 */     return this.hashCode;
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\cache\value\FCValueCacheKey.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */