package com.opentext.contivo.callcommand.fulcrum;

import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Map;

public interface IPerformanceMetrics {
  void incre(String paramString, long paramLong);
  
  String getMetricsAsJson() throws JsonProcessingException;
  
  Map<String, Long> getMetrics();
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-*******.jar!\com\opentext\contivo\callcommand\fulcrum\IPerformanceMetrics.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */