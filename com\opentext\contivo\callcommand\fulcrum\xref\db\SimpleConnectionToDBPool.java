/*    */ package com.opentext.contivo.callcommand.fulcrum.xref.db;
/*    */ 
/*    */ import com.contivo.mixedruntime.RuntimeMessageException;
/*    */ import com.contivo.transform.MessageInfo;
/*    */ import com.contivo.transform.Tag;
/*    */ import java.sql.Connection;
/*    */ import java.sql.Driver;
/*    */ import java.sql.SQLException;
/*    */ import java.util.Properties;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SimpleConnectionToDBPool
/*    */   implements DBPool
/*    */ {
/*    */   private final String url;
/*    */   private final String username;
/*    */   private final String password;
/*    */   private Driver driver;
/*    */   
/*    */   public SimpleConnectionToDBPool(String url, String driverName, String username, String password) throws RuntimeMessageException {
/* 34 */     this.url = url;
/* 35 */     this.username = username;
/* 36 */     this.password = password;
/*    */     
/*    */     try {
/* 39 */       this.driver = (Driver)Class.forName(driverName).newInstance();
/*    */     }
/* 41 */     catch (Exception e) {
/*    */       
/* 43 */       MessageInfo info = new MessageInfo("SimpleConnectionToDBPool.init", new Tag[] { Tag.FATAL });
/* 44 */       throw new RuntimeMessageException(info, "Unable to find driver class: " + driverName);
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Connection getConnection() throws SQLException {
/* 51 */     Properties props = new Properties();
/* 52 */     if (this.username != null)
/*    */     {
/* 54 */       props.put("user", this.username);
/*    */     }
/* 56 */     if (this.password != null)
/*    */     {
/* 58 */       props.put("password", this.password);
/*    */     }
/* 60 */     return this.driver.connect(this.url, props);
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\db\SimpleConnectionToDBPool.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */