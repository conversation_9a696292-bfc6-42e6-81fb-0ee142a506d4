/*    */ package com.opentext.contivo.callcommand.fulcrum;
/*    */ 
/*    */ import com.fasterxml.jackson.core.JsonProcessingException;
/*    */ import com.fasterxml.jackson.databind.ObjectMapper;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ 
/*    */ public class PerformanceMetrics
/*    */   implements IPerformanceMetrics {
/* 10 */   private static final ObjectMapper mapper = new ObjectMapper();
/* 11 */   Map<String, Long> metrics = new HashMap<>();
/*    */   
/*    */   public void incre(String key, long val) {
/* 14 */     this.metrics.merge(key, Long.valueOf(val), (a, b) -> Long.valueOf(a.longValue() + b.longValue()));
/*    */   }
/*    */ 
/*    */   
/*    */   public String getMetricsAsJson() throws JsonProcessingException {
/* 19 */     return mapper.writeValueAsString(this.metrics);
/*    */   }
/*    */ 
/*    */   
/*    */   public Map<String, Long> getMetrics() {
/* 24 */     return this.metrics;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/*    */     try {
/* 30 */       return getMetricsAsJson();
/* 31 */     } catch (JsonProcessingException e) {
/* 32 */       return "";
/*    */     } 
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-*******.jar!\com\opentext\contivo\callcommand\fulcrum\PerformanceMetrics.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */