/*    */ package com.opentext.contivo.callcommand.fulcrum.db;
/*    */ 
/*    */ import java.sql.Connection;
/*    */ import java.sql.PreparedStatement;
/*    */ import java.sql.ResultSet;
/*    */ import java.sql.SQLException;
/*    */ 
/*    */ 
/*    */ public class RefNoApi
/*    */ {
/*    */   public static final String NAME = "name";
/*    */   public static final String VALUE = "\"VALUE\"";
/*    */   public static final String FLDREFNO = "ec_ref_no";
/*    */   private static final String UPD_REFNO_SQL_STMT = "UPDATE ec_ref_no SET \"VALUE\"= \"VALUE\"+ 1 WHERE NAME = ?";
/*    */   private static final String GET_REFNO_SQL_STMT = "SELECT \"VALUE\" FROM ec_ref_no WHERE NAME = ?";
/*    */   private static final String INSERT_REFNO_SQL_STMT = "INSERT INTO ec_ref_no ( name, \"VALUE\") values ( ?, ? )";
/*    */   
/*    */   public static int updateRefNo(Connection conn, String refNo_name) throws SQLException {
/* 19 */     PreparedStatement pStmt = null;
/* 20 */     int result = 0;
/*    */     
/*    */     try {
/* 23 */       pStmt = conn.prepareStatement("UPDATE ec_ref_no SET \"VALUE\"= \"VALUE\"+ 1 WHERE NAME = ?", 1004, 1007);
/*    */       
/* 25 */       pStmt.setString(1, refNo_name);
/* 26 */       result = pStmt.executeUpdate();
/*    */     }
/*    */     finally {
/*    */       
/* 30 */       if (pStmt != null)
/*    */       {
/* 32 */         pStmt.close();
/*    */       }
/*    */     } 
/* 35 */     return result;
/*    */   }
/*    */ 
/*    */   
/*    */   public static void insertRefNo(Connection conn, String refNo_name) throws SQLException {
/* 40 */     PreparedStatement pStmt = null;
/*    */     
/*    */     try {
/* 43 */       pStmt = conn.prepareStatement("INSERT INTO ec_ref_no ( name, \"VALUE\") values ( ?, ? )", 1004, 1007);
/*    */       
/* 45 */       pStmt.setString(1, refNo_name);
/* 46 */       pStmt.setLong(2, 1L);
/*    */ 
/*    */       
/* 49 */       if (System.getProperty("mapRepository", "RMI").equals("JDBC") && 
/* 50 */         System.getProperty("VJDBCEnabled", "no").equals("yes"))
/*    */       {
/* 52 */         pStmt.executeUpdate();
/*    */       }
/*    */       else
/*    */       {
/* 56 */         pStmt.execute();
/*    */       }
/*    */     
/*    */     } finally {
/*    */       
/* 61 */       if (pStmt != null)
/*    */       {
/* 63 */         pStmt.close();
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public static long selectRefNo(Connection conn, String refNo_name) throws Exception {
/* 70 */     PreparedStatement pStmt = null;
/*    */     
/*    */     try {
/* 73 */       pStmt = conn.prepareStatement("SELECT \"VALUE\" FROM ec_ref_no WHERE NAME = ?", 1004, 1007);
/*    */       
/* 75 */       pStmt.setString(1, refNo_name);
/*    */       
/* 77 */       ResultSet rset = pStmt.executeQuery();
/*    */       
/* 79 */       if (rset.next()) {
/* 80 */         return rset.getLong(1);
/*    */       }
/*    */       
/* 83 */       throw new Exception("No reference number returned from database table ec_ref_no/" + refNo_name + " for auditing purposes.");
/*    */     
/*    */     }
/*    */     finally {
/*    */       
/* 88 */       if (pStmt != null)
/*    */       {
/* 90 */         pStmt.close();
/*    */       }
/*    */     } 
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\db\RefNoApi.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */