/*    */ package com.opentext.contivo.callcommand.fulcrum.xref.cache.value;
/*    */ 
/*    */ import com.google.common.cache.Cache;
/*    */ import com.google.common.cache.CacheBuilder;
/*    */ import java.util.Optional;
/*    */ import java.util.concurrent.TimeUnit;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FCValueGuavaCache
/*    */   implements FCValueCache
/*    */ {
/*    */   private static final int MAX_SIZE = 10000;
/*    */   private final Cache<FCValueCacheKey, Optional<String>> cache;
/*    */   
/*    */   public FCValueGuavaCache(long expirationTime, long maxSize) {
/* 23 */     this
/*    */ 
/*    */       
/* 26 */       .cache = CacheBuilder.newBuilder().expireAfterAccess(expirationTime, TimeUnit.SECONDS).maximumSize(maxSize).build();
/*    */   }
/*    */   
/*    */   public FCValueGuavaCache() {
/* 30 */     this
/*    */       
/* 32 */       .cache = CacheBuilder.newBuilder().maximumSize(10000L).build();
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public Optional<String> get(FCValueCacheKey key) {
/* 38 */     return (Optional<String>)this.cache.getIfPresent(key);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void put(FCValueCacheKey key, Optional<String> value) {
/* 44 */     this.cache.put(key, value);
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\cache\value\FCValueGuavaCache.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */