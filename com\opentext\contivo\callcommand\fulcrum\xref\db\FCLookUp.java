/*     */ package com.opentext.contivo.callcommand.fulcrum.xref.db;
/*     */ 
/*     */ import com.contivo.mixedruntime.RuntimeMessageException;
/*     */ import com.contivo.transform.MessageInfo;
/*     */ import com.contivo.transform.Tag;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.LookUpClient;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.Op;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.Table;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.cache.table.FCTableCache;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.cache.table.FCTableCacheKey;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.cache.table.FCTableGuavaCache;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.cache.value.FCValueCache;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.cache.value.FCValueCacheKey;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.cache.value.FCValueGuavaCache;
/*     */ import java.io.IOException;
/*     */ import java.sql.Connection;
/*     */ import java.sql.SQLException;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Objects;
/*     */ import java.util.Optional;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FCLookUp
/*     */   implements LookUpClient
/*     */ {
/*     */   private final FCTableCache tblCache;
/*     */   private final FCValueCache valCache;
/*     */   private final DBPool connectionPool;
/*     */   private final int tableSizeThreshold;
/*     */   
/*     */   public FCLookUp(DBPool connectionPool) {
/*  50 */     this.connectionPool = Objects.<DBPool>requireNonNull(connectionPool);
/*  51 */     this.tblCache = (FCTableCache)new FCTableGuavaCache();
/*  52 */     this.valCache = (FCValueCache)new FCValueGuavaCache();
/*  53 */     this.tableSizeThreshold = 100;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public FCLookUp(DBPool connectionPool, FCTableCache tblCache, FCValueCache valCache, int tableSizeThreshold) throws RuntimeMessageException {
/*  67 */     this.connectionPool = Objects.<DBPool>requireNonNull(connectionPool);
/*  68 */     this.tblCache = Objects.<FCTableCache>requireNonNull(tblCache);
/*  69 */     this.valCache = Objects.<FCValueCache>requireNonNull(valCache);
/*  70 */     this.tableSizeThreshold = tableSizeThreshold;
/*  71 */     if (tableSizeThreshold <= 0) {
/*     */       
/*  73 */       MessageInfo info = new MessageInfo("FCLookUp.init", new Tag[] { Tag.FATAL });
/*  74 */       throw new RuntimeMessageException(info, "Threshold must be greater than 0");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String lookupValue(String solutionID, String tableName, String[] findCols, String resultCol, String[] findValues, Op[] ops, boolean useColName) throws Exception {
/*  90 */     FCTableCacheKey tableKey = new FCTableCacheKey(solutionID, tableName);
/*  91 */     FCValueCacheKey valKey = new FCValueCacheKey(tableKey, findCols, resultCol, findValues, ops);
/*     */     
/*  93 */     Optional<String> cachedValue = this.valCache.get(valKey);
/*  94 */     if (cachedValue == null) {
/*     */       
/*  96 */       Table table = this.tblCache.get(tableKey);
/*     */       
/*  98 */       if (table == null) {
/*     */ 
/*     */         
/* 101 */         Map<String, Integer> headers = getTableHeaders(solutionID, tableName);
/* 102 */         List<Map<Integer, String>> rows = getTableRows(solutionID, tableName);
/* 103 */         table = new Table(solutionID, tableName, headers, rows, false);
/* 104 */         this.tblCache.put(tableKey, table);
/*     */       } 
/*     */       
/* 107 */       if (table.getRowSize() == 0 && !checkTableExist(solutionID, tableName))
/*     */       {
/* 109 */         throw new Exception("Table '" + tableName + "' in solutionID '" + solutionID + "' does not exist");
/*     */       }
/* 111 */       String ret = table.findValue(findCols, resultCol, findValues, ops, useColName);
/*     */       
/* 113 */       if (ret == null && table.getRowSize() > this.tableSizeThreshold)
/*     */       {
/* 115 */         if (useColName) {
/* 116 */           String[] convertedFromColName = new String[findCols.length];
/* 117 */           String convertedResColName = null;
/*     */           
/* 119 */           if (table.getHeaders().isEmpty()) {
/* 120 */             throw new Exception("Table " + tableName + " in " + solutionID + " does not have a header row, but you are attemping to do a lookup by column name. Ensure that the checkbox that says \"Contains Header Row\" is checked when importing through TGTS UI or use the LoadXref service and specify the Header in the policy definition.");
/*     */           }
/*     */ 
/*     */           
/* 124 */           for (int i = 0; i < findCols.length; i++) {
/* 125 */             Integer integer = (Integer)table.getHeaders().get(findCols[i]);
/* 126 */             if (integer == null) {
/* 127 */               throw new Exception("Expected a column name for parameter 1 (findCols) index [" + i + 1 + "]. However '" + findCols[i] + "' does not match the name of any columns.");
/*     */             }
/*     */             
/* 130 */             convertedFromColName[i] = ((Integer)table.getHeaders().get(findCols[i])).toString();
/*     */           } 
/* 132 */           Integer _header = (Integer)table.getHeaders().get(resultCol);
/* 133 */           if (_header == null) {
/* 134 */             throw new Exception("Expected a column name for parameter 2 (resultCol). However '" + resultCol + "' does not match the name of any columns.");
/*     */           }
/*     */ 
/*     */           
/* 138 */           convertedResColName = _header.toString();
/*     */ 
/*     */           
/* 141 */           ret = getLookupValue(solutionID, tableName, convertedFromColName, convertedResColName, findValues, ops);
/*     */         } else {
/*     */           
/* 144 */           ret = getLookupValue(solutionID, tableName, findCols, resultCol, findValues, ops);
/*     */         } 
/*     */       }
/* 147 */       this.valCache.put(valKey, Optional.ofNullable(ret));
/* 148 */       return ret;
/*     */     } 
/*     */     
/* 151 */     return cachedValue.orElse(null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getLookupValue(String solutionID, String tableName, String[] findCols, String resultCol, String[] findValues, Op[] ops) throws Exception {
/* 158 */     Connection con = this.connectionPool.getConnection();
/*     */     
/* 160 */     try { String str = FCDBApi.selectLookupValue(con, solutionID, tableName, findCols, resultCol, findValues, ops);
/* 161 */       if (con != null) con.close();  return str; }
/*     */     catch (Throwable throwable) { if (con != null)
/*     */         try { con.close(); }
/*     */         catch (Throwable throwable1) { throwable.addSuppressed(throwable1); }
/*     */           throw throwable; }
/* 166 */      } private Map<String, Integer> getTableHeaders(String solutionID, String tableName) throws SQLException, IOException { Connection con = this.connectionPool.getConnection();
/*     */     
/* 168 */     try { Map<String, Integer> map = FCDBApi.getHeaders(con, solutionID, tableName);
/* 169 */       if (con != null) con.close();  return map; }
/*     */     catch (Throwable throwable) { if (con != null)
/*     */         try { con.close(); }
/*     */         catch (Throwable throwable1) { throwable.addSuppressed(throwable1); }
/*     */           throw throwable; }
/* 174 */      } private List<Map<Integer, String>> getTableRows(String solutionID, String tableName) throws SQLException, IOException { Connection con = this.connectionPool.getConnection();
/*     */     
/* 176 */     try { List<Map<Integer, String>> list = FCDBApi.getTable(con, solutionID, tableName, this.tableSizeThreshold + 1);
/* 177 */       if (con != null) con.close();  return list; } catch (Throwable throwable) { if (con != null)
/*     */         try { con.close(); }
/*     */         catch (Throwable throwable1) { throwable.addSuppressed(throwable1); }
/*     */           throw throwable; }
/* 181 */      } private boolean checkTableExist(String solutionID, String tableName) throws SQLException, IOException { Connection con = this.connectionPool.getConnection();
/*     */     try {
/* 183 */       boolean bool = FCDBApi.checkTableExists(con, solutionID, tableName);
/* 184 */       if (con != null) con.close(); 
/*     */       return bool;
/*     */     } catch (Throwable throwable) {
/*     */       if (con != null)
/*     */         try {
/*     */           con.close();
/*     */         } catch (Throwable throwable1) {
/*     */           throwable.addSuppressed(throwable1);
/*     */         }  
/*     */       throw throwable;
/*     */     }  }
/*     */ 
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\xref\db\FCLookUp.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */