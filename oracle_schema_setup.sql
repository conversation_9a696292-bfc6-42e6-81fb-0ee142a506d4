-- Oracle Database Schema Setup for FCXRef Database Migration
-- This script creates the required tables and sequences for database-based lookups

-- Create sequences for primary key generation
CREATE SEQUENCE TGTS_LOOKUP_SEQ 
    START WITH 1000 
    INCREMENT BY 1 
    NOCACHE 
    NOCYCLE;

CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ 
    START WITH 2000 
    INCREMENT BY 1 
    NOCACHE 
    NOCYCLE;

-- Main lookup table - stores metadata for each lookup table
CREATE TABLE TGTS_LOOKUP (
    TABLE_ID        NUMBER(19) PRIMARY KEY,
    SOLUTION_ID     VARCHAR2(255) NOT NULL,
    TABLE_NAME      VARCHAR2(255) NOT NULL,
    CREATE_DATE     TIMESTAMP NOT NULL,
    A<PERSON><PERSON>ABLE_DATE  TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IDX_TGTS_LOOKUP_SOLUTION_TABLE ON TGTS_LOOKUP (SOLUTION_ID, TABLE_NAME);
CREATE INDEX IDX_TGTS_LOOKUP_CREATE_DATE ON TGTS_LOOKUP (CREATE_DATE);

-- Row metadata table - stores information about each row
CREATE TABLE TGTS_LOOKUP_ROW (
    ROW_ID       NUMBER(19) PRIMARY KEY,
    FK_TABLE_ID  NUMBER(19) NOT NULL,
    ROW_TYPE     NUMBER(1) NOT NULL,
    CONSTRAINT FK_LOOKUP_ROW_TABLE 
        FOREIGN KEY (FK_TABLE_ID) REFERENCES TGTS_LOOKUP(TABLE_ID) ON DELETE CASCADE,
    CONSTRAINT CHK_ROW_TYPE 
        CHECK (ROW_TYPE IN (0, 1))  -- 0=data row, 1=header row
);

-- Create indexes for performance
CREATE INDEX IDX_TGTS_LOOKUP_ROW_TABLE ON TGTS_LOOKUP_ROW (FK_TABLE_ID);
CREATE INDEX IDX_TGTS_LOOKUP_ROW_TYPE ON TGTS_LOOKUP_ROW (FK_TABLE_ID, ROW_TYPE);

-- Column data table - stores actual column values
CREATE TABLE TGTS_LOOKUP_COLUMN (
    FK_ROW_ID  NUMBER(19) NOT NULL,
    COL_NUM    NUMBER(10) NOT NULL,
    COL_VAL    VARCHAR2(4000),
    CONSTRAINT PK_LOOKUP_COLUMN 
        PRIMARY KEY (FK_ROW_ID, COL_NUM),
    CONSTRAINT FK_LOOKUP_COL_ROW 
        FOREIGN KEY (FK_ROW_ID) REFERENCES TGTS_LOOKUP_ROW(ROW_ID) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IDX_TGTS_LOOKUP_COL_ROW ON TGTS_LOOKUP_COLUMN (FK_ROW_ID);
CREATE INDEX IDX_TGTS_LOOKUP_COL_VAL ON TGTS_LOOKUP_COLUMN (COL_VAL);

-- Comments for documentation
COMMENT ON TABLE TGTS_LOOKUP IS 'Stores metadata for lookup tables including versioning information';
COMMENT ON COLUMN TGTS_LOOKUP.TABLE_ID IS 'Unique identifier for each table version';
COMMENT ON COLUMN TGTS_LOOKUP.SOLUTION_ID IS 'Solution identifier for data partitioning';
COMMENT ON COLUMN TGTS_LOOKUP.TABLE_NAME IS 'Logical name of the lookup table';
COMMENT ON COLUMN TGTS_LOOKUP.CREATE_DATE IS 'Timestamp when this table version was created';
COMMENT ON COLUMN TGTS_LOOKUP.AVAILABLE_DATE IS 'Optional date when this table becomes available';

COMMENT ON TABLE TGTS_LOOKUP_ROW IS 'Stores row metadata for lookup tables';
COMMENT ON COLUMN TGTS_LOOKUP_ROW.ROW_ID IS 'Unique identifier for each row';
COMMENT ON COLUMN TGTS_LOOKUP_ROW.FK_TABLE_ID IS 'References TGTS_LOOKUP.TABLE_ID';
COMMENT ON COLUMN TGTS_LOOKUP_ROW.ROW_TYPE IS '0=data row, 1=header row';

COMMENT ON TABLE TGTS_LOOKUP_COLUMN IS 'Stores actual column data for lookup tables';
COMMENT ON COLUMN TGTS_LOOKUP_COLUMN.FK_ROW_ID IS 'References TGTS_LOOKUP_ROW.ROW_ID';
COMMENT ON COLUMN TGTS_LOOKUP_COLUMN.COL_NUM IS 'Column number (1-based indexing)';
COMMENT ON COLUMN TGTS_LOOKUP_COLUMN.COL_VAL IS 'Column value (up to 4000 characters)';

-- Grant permissions (adjust as needed for your environment)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON TGTS_LOOKUP TO your_application_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON TGTS_LOOKUP_ROW TO your_application_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON TGTS_LOOKUP_COLUMN TO your_application_user;
-- GRANT SELECT ON TGTS_LOOKUP_SEQ TO your_application_user;
-- GRANT SELECT ON TGTS_LOOKUP_ROW_SEQ TO your_application_user;

-- Sample data insertion procedure (for testing)
CREATE OR REPLACE PROCEDURE INSERT_SAMPLE_LOOKUP_DATA AS
    v_table_id NUMBER;
    v_header_row_id NUMBER;
    v_data_row_id NUMBER;
BEGIN
    -- Insert table metadata
    INSERT INTO TGTS_LOOKUP (TABLE_ID, SOLUTION_ID, TABLE_NAME, CREATE_DATE, AVAILABLE_DATE)
    VALUES (TGTS_LOOKUP_SEQ.NEXTVAL, 'TEST_SOLUTION', 'wo1_CONTIVO', SYSTIMESTAMP, NULL)
    RETURNING TABLE_ID INTO v_table_id;
    
    -- Insert header row
    INSERT INTO TGTS_LOOKUP_ROW (ROW_ID, FK_TABLE_ID, ROW_TYPE)
    VALUES (TGTS_LOOKUP_ROW_SEQ.NEXTVAL, v_table_id, 1)
    RETURNING ROW_ID INTO v_header_row_id;
    
    -- Insert header columns
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_header_row_id, 1, 'wo1_CONTIVO');
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_header_row_id, 2, '');
    
    -- Insert data rows
    -- Row 1: A -> Apple
    INSERT INTO TGTS_LOOKUP_ROW (ROW_ID, FK_TABLE_ID, ROW_TYPE)
    VALUES (TGTS_LOOKUP_ROW_SEQ.NEXTVAL, v_table_id, 0)
    RETURNING ROW_ID INTO v_data_row_id;
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_data_row_id, 1, 'A');
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_data_row_id, 2, 'Apple');
    
    -- Row 2: MES-EMFC -> EMFC
    INSERT INTO TGTS_LOOKUP_ROW (ROW_ID, FK_TABLE_ID, ROW_TYPE)
    VALUES (TGTS_LOOKUP_ROW_SEQ.NEXTVAL, v_table_id, 0)
    RETURNING ROW_ID INTO v_data_row_id;
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_data_row_id, 1, 'MES-EMFC');
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_data_row_id, 2, 'EMFC');
    
    -- Row 3: MES-AMFF -> AMFF
    INSERT INTO TGTS_LOOKUP_ROW (ROW_ID, FK_TABLE_ID, ROW_TYPE)
    VALUES (TGTS_LOOKUP_ROW_SEQ.NEXTVAL, v_table_id, 0)
    RETURNING ROW_ID INTO v_data_row_id;
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_data_row_id, 1, 'MES-AMFF');
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_data_row_id, 2, 'AMFF');
    
    -- Row 4: COMPAL -> D
    INSERT INTO TGTS_LOOKUP_ROW (ROW_ID, FK_TABLE_ID, ROW_TYPE)
    VALUES (TGTS_LOOKUP_ROW_SEQ.NEXTVAL, v_table_id, 0)
    RETURNING ROW_ID INTO v_data_row_id;
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_data_row_id, 1, 'COMPAL');
    INSERT INTO TGTS_LOOKUP_COLUMN (FK_ROW_ID, COL_NUM, COL_VAL) VALUES (v_data_row_id, 2, 'D');
    
    COMMIT;
    DBMS_OUTPUT.PUT_LINE('Sample lookup data inserted successfully');
END;
/

-- Verification queries
-- Use these to verify the schema and data
/*
-- Check table structure
SELECT table_name, column_name, data_type, data_length 
FROM user_tab_columns 
WHERE table_name IN ('TGTS_LOOKUP', 'TGTS_LOOKUP_ROW', 'TGTS_LOOKUP_COLUMN')
ORDER BY table_name, column_id;

-- Check constraints
SELECT constraint_name, constraint_type, table_name, search_condition
FROM user_constraints
WHERE table_name IN ('TGTS_LOOKUP', 'TGTS_LOOKUP_ROW', 'TGTS_LOOKUP_COLUMN');

-- Check indexes
SELECT index_name, table_name, column_name
FROM user_ind_columns
WHERE table_name IN ('TGTS_LOOKUP', 'TGTS_LOOKUP_ROW', 'TGTS_LOOKUP_COLUMN')
ORDER BY index_name, column_position;

-- Test the sample data (after running INSERT_SAMPLE_LOOKUP_DATA)
SELECT 
    l.solution_id,
    l.table_name,
    r.row_type,
    c.col_num,
    c.col_val
FROM tgts_lookup l
JOIN tgts_lookup_row r ON l.table_id = r.fk_table_id
JOIN tgts_lookup_column c ON r.row_id = c.fk_row_id
ORDER BY l.table_id, r.row_type DESC, r.row_id, c.col_num;
*/

-- Performance monitoring views
CREATE OR REPLACE VIEW V_LOOKUP_TABLE_STATS AS
SELECT 
    l.solution_id,
    l.table_name,
    l.create_date,
    COUNT(CASE WHEN r.row_type = 1 THEN 1 END) as header_rows,
    COUNT(CASE WHEN r.row_type = 0 THEN 1 END) as data_rows,
    MAX(c.col_num) as max_columns
FROM tgts_lookup l
LEFT JOIN tgts_lookup_row r ON l.table_id = r.fk_table_id
LEFT JOIN tgts_lookup_column c ON r.row_id = c.fk_row_id
GROUP BY l.solution_id, l.table_name, l.create_date, l.table_id
ORDER BY l.solution_id, l.table_name, l.create_date DESC;

COMMENT ON VIEW V_LOOKUP_TABLE_STATS IS 'Provides statistics about lookup tables for monitoring and performance analysis';

-- Cleanup procedure (for testing/development)
CREATE OR REPLACE PROCEDURE CLEANUP_LOOKUP_DATA(p_solution_id VARCHAR2 DEFAULT NULL) AS
BEGIN
    IF p_solution_id IS NOT NULL THEN
        DELETE FROM tgts_lookup WHERE solution_id = p_solution_id;
        DBMS_OUTPUT.PUT_LINE('Cleaned up data for solution: ' || p_solution_id);
    ELSE
        DELETE FROM tgts_lookup;
        DBMS_OUTPUT.PUT_LINE('Cleaned up all lookup data');
    END IF;
    
    -- Reset sequences
    EXECUTE IMMEDIATE 'DROP SEQUENCE TGTS_LOOKUP_SEQ';
    EXECUTE IMMEDIATE 'DROP SEQUENCE TGTS_LOOKUP_ROW_SEQ';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000 INCREMENT BY 1 NOCACHE NOCYCLE';
    EXECUTE IMMEDIATE 'CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ START WITH 2000 INCREMENT BY 1 NOCACHE NOCYCLE';
    
    COMMIT;
END;
/

COMMIT;
