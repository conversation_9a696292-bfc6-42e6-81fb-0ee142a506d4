# Test Dependencies for FCXRefCallCmd Migration

## Required Dependencies for Unit Testing

To run the unit tests for the FCXRefCallCmd migration project, you need to add the following dependencies to your project:

### Maven Dependencies (pom.xml)

```xml
<dependencies>
    <!-- JUnit 5 for unit testing -->
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>5.9.2</version>
        <scope>test</scope>
    </dependency>
    
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>5.9.2</version>
        <scope>test</scope>
    </dependency>
    
    <!-- H2 Database for in-memory testing -->
    <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>2.1.214</version>
        <scope>test</scope>
    </dependency>
    
    <!-- Oracle JDBC Driver (for production) -->
    <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>21.7.0.0</version>
    </dependency>
    
    <!-- Guava for caching (if using FCLookUp) -->
    <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>31.1-jre</version>
    </dependency>
    
    <!-- FunctionalJava for P2 tuple class -->
    <dependency>
        <groupId>org.functionaljava</groupId>
        <artifactId>functionaljava</artifactId>
        <version>4.9</version>
    </dependency>
</dependencies>

<build>
    <plugins>
        <!-- Surefire plugin for running tests -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>3.0.0-M9</version>
            <configuration>
                <includes>
                    <include>**/*Test.java</include>
                </includes>
            </configuration>
        </plugin>
    </plugins>
</build>
```

### Gradle Dependencies (build.gradle)

```gradle
dependencies {
    // JUnit 5 for unit testing
    testImplementation 'org.junit.jupiter:junit-jupiter-engine:5.9.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.9.2'
    
    // H2 Database for in-memory testing
    testImplementation 'com.h2database:h2:2.1.214'
    
    // Oracle JDBC Driver (for production)
    implementation 'com.oracle.database.jdbc:ojdbc8:21.7.0.0'
    
    // Guava for caching
    implementation 'com.google.guava:guava:31.1-jre'
    
    // FunctionalJava for P2 tuple class
    implementation 'org.functionaljava:functionaljava:4.9'
}

test {
    useJUnitPlatform()
}
```

## Test Files and Their Dependencies

### 1. FCXRefMigrationTest.java
**Dependencies:**
- JUnit 5 (jupiter-engine, jupiter-api)
- H2 Database (h2)
- FCXRefCallCmd classes (from main project)

**Purpose:** Comprehensive validation of file vs database lookup behavior

### 2. MigrationUtilityTest.java
**Dependencies:**
- JUnit 5 (jupiter-engine, jupiter-api)
- H2 Database (h2)
- FileToDatabaseMigrationUtility class

**Purpose:** Tests the migration utility compilation and basic functionality

### 3. DatabaseCompatibilityTest.java
**Dependencies:**
- JUnit 5 (jupiter-engine, jupiter-api)
- H2 Database (h2)
- FileToDatabaseMigrationUtility class

**Purpose:** Verifies database compatibility between Oracle and H2 syntax

## Running the Tests

### Command Line (Maven)
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=FCXRefMigrationTest
mvn test -Dtest=MigrationUtilityTest
mvn test -Dtest=DatabaseCompatibilityTest

# Run tests with verbose output
mvn test -Dtest=FCXRefMigrationTest -Dmaven.test.failure.ignore=true -X
```

### Command Line (Gradle)
```bash
# Run all tests
./gradlew test

# Run specific test class
./gradlew test --tests FCXRefMigrationTest
./gradlew test --tests MigrationUtilityTest
./gradlew test --tests DatabaseCompatibilityTest

# Run tests with verbose output
./gradlew test --info
```

### IDE Configuration

#### IntelliJ IDEA
1. **Import Dependencies:** Ensure Maven/Gradle dependencies are imported
2. **JUnit Configuration:** Go to Run/Debug Configurations → JUnit
3. **Test Runner:** Select "JUnit 5" as the test runner
4. **Classpath:** Ensure H2 and other test dependencies are in classpath

#### Eclipse
1. **Import Dependencies:** Right-click project → Maven → Reload Projects
2. **JUnit Configuration:** Run As → JUnit Test
3. **Build Path:** Ensure test dependencies are in build path

#### VS Code
1. **Java Extension Pack:** Install Java Extension Pack
2. **Test Runner:** Use Java Test Runner extension
3. **Dependencies:** Ensure Maven/Gradle extension loads dependencies

## Database Configuration for Tests

### H2 In-Memory Database
The tests use H2 in-memory database with these settings:

```java
private static final String DB_URL = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1";
private static final String DB_DRIVER = "org.h2.Driver";
```

**Benefits:**
- **Fast:** In-memory database for quick test execution
- **Isolated:** Each test gets a fresh database instance
- **No Setup:** No external database installation required
- **Portable:** Works on any machine with Java

### Oracle Database (Production)
For integration testing with actual Oracle database:

```java
private static final String DB_URL = "***********************************";
private static final String DB_DRIVER = "oracle.jdbc.driver.OracleDriver";
private static final String DB_USER = "your_username";
private static final String DB_PASSWORD = "your_password";
```

## Troubleshooting

### Common Issues

#### 1. H2 Database Not Found
**Error:** `ClassNotFoundException: org.h2.Driver`
**Solution:** Add H2 dependency to test scope

#### 2. JUnit Tests Not Running
**Error:** Tests not discovered or executed
**Solution:** 
- Ensure JUnit 5 dependencies are present
- Check test class naming convention (*Test.java)
- Verify @Test annotations are from org.junit.jupiter.api.Test

#### 3. Oracle Driver Issues
**Error:** `ClassNotFoundException: oracle.jdbc.driver.OracleDriver`
**Solution:** 
- Add Oracle JDBC driver dependency
- For Maven, may need to install Oracle driver manually if not in public repos

#### 4. Sequence Syntax Errors
**Error:** `Column "TGTS_LOOKUP_SEQ.NEXTVAL" not found`
**Solution:** 
- Ensure you're using the updated FileToDatabaseMigrationUtility.java with database detection
- Verify H2 sequences are created with correct syntax

### Debugging Tips

#### 1. Enable SQL Logging
For H2 database, add to connection URL:
```java
"jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;TRACE_LEVEL_SYSTEM_OUT=2"
```

#### 2. Check Database Type Detection
```java
try (Connection conn = DriverManager.getConnection(DB_URL)) {
    String productName = conn.getMetaData().getDatabaseProductName();
    System.out.println("Detected database: " + productName);
}
```

#### 3. Verify Schema Creation
```java
try (Connection conn = DriverManager.getConnection(DB_URL)) {
    DatabaseMetaData metaData = conn.getMetaData();
    ResultSet tables = metaData.getTables(null, null, "TGTS_LOOKUP%", null);
    while (tables.next()) {
        System.out.println("Table: " + tables.getString("TABLE_NAME"));
    }
}
```

## Test Execution Order

### Recommended Test Sequence
1. **DatabaseCompatibilityTest** - Verify database detection and syntax
2. **MigrationUtilityTest** - Test migration utility functionality
3. **FCXRefMigrationTest** - Comprehensive validation of lookup behavior

### Parallel Execution
Tests can run in parallel since each uses isolated H2 in-memory databases:

```xml
<!-- Maven Surefire parallel execution -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <configuration>
        <parallel>classes</parallel>
        <threadCount>4</threadCount>
    </configuration>
</plugin>
```

## Performance Expectations

### Test Execution Times
- **DatabaseCompatibilityTest:** ~1-2 seconds
- **MigrationUtilityTest:** ~2-3 seconds  
- **FCXRefMigrationTest:** ~5-10 seconds (comprehensive validation)

### Memory Usage
- **H2 In-Memory:** ~10-50 MB per test database
- **Test Data:** Minimal (small CSV files)
- **Total:** Should run comfortably with 512 MB heap

## Continuous Integration

### GitHub Actions Example
```yaml
name: FCXRef Migration Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
    
    - name: Run tests
      run: mvn test
```

This test setup ensures that the FCXRefCallCmd migration functionality is thoroughly tested across different database platforms while maintaining compatibility with both Oracle (production) and H2 (testing) environments.
