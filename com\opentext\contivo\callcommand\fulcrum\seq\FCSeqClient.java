package com.opentext.contivo.callcommand.fulcrum.seq;

public interface FCSeqClient {
  long getDbSequenceNumber(long paramLong, String paramString1, String paramString2, String paramString3, String paramString4) throws Exception;
  
  void setDbSequenceNumber(long paramLong, String paramString1, String paramString2, String paramString3, String paramString4) throws Exception;
}


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\FCSeqClient.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */