/*     */ package com.opentext.contivo.callcommand.fulcrum.enrichment;
/*     */ 
/*     */ import com.contivo.analyst.util.ContivoExternalClass;
/*     */ import com.contivo.analyst.util.ContivoExternalMethod;
/*     */ import com.contivo.mixedruntime.RuntimeMessageException;
/*     */ import com.contivo.runtime.core.IContivoRuntime;
/*     */ import com.contivo.runtime.core.IContivoRuntimeSelfTimeout;
/*     */ import com.contivo.transform.MessageInfo;
/*     */ import com.contivo.transform.Tag;
/*     */ import com.opentext.contivo.callcommand.fulcrum.EnvProp;
/*     */ import com.opentext.contivo.callcommand.fulcrum.EnvironmentQuery;
/*     */ import com.opentext.contivo.callcommand.fulcrum.IPerformanceMetrics;
/*     */ import com.opentext.contivo.callcommand.fulcrum.enrichment.db.FCDBEnrich;
/*     */ import com.opentext.contivo.callcommand.fulcrum.enrichment.local.FCLocalEnrich;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.DBPool;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.SimpleConnectionToDBPool;
/*     */ import java.io.File;
/*     */ import java.util.Optional;
/*     */ import java.util.Properties;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @ContivoExternalClass
/*     */ public class FCEnrichCallCmd
/*     */   implements IContivoRuntimeSelfTimeout
/*     */ {
/*     */   public static final String DB_URL_PROP = "fc.enrich.db.url";
/*     */   public static final String DRIVER_NAME_PROP = "fc.enrich.db.driver";
/*     */   public static final String DB_USER_PROP = "fc.enrich.db.user";
/*     */   public static final String DB_PASSWORD_PROP = "fc.enrich.db.password";
/*     */   public static final String FS_PATH_PROP = "fc.enrich.fs.path";
/*     */   private FCEnrichProp props;
/*     */   private IDbEnrichClient client;
/*     */   private IPerformanceMetrics metrics;
/*     */   
/*     */   private void init(IContivoRuntime icr) throws RuntimeMessageException {
/*  63 */     if (this.client != null) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/*  68 */     MessageInfo info = new MessageInfo("FCEnrichCallCmd.init", new Tag[] { Tag.FATAL });
/*     */ 
/*     */     
/*  71 */     this.client = (IDbEnrichClient)icr.getInstance(IDbEnrichClient.class);
/*  72 */     this.props = (FCEnrichProp)icr.getInstance(FCEnrichProp.class);
/*  73 */     this.metrics = (IPerformanceMetrics)icr.getInstance(IPerformanceMetrics.class);
/*  74 */     Properties envJavaProp = EnvironmentQuery.getEnvJavaProp(icr);
/*  75 */     EnvProp envProp = EnvironmentQuery.getEnvProp(icr, envJavaProp);
/*     */ 
/*     */ 
/*     */     
/*  79 */     Properties prop = icr.getUserProperties();
/*     */     
/*  81 */     if (this.props == null)
/*     */     {
/*  83 */       if (envProp != null) {
/*  84 */         this.props = new FCEnrichProp(envProp.getSolutionID(), envProp.isTraceMode());
/*     */       } else {
/*  86 */         String solutionID = (String)Optional.<String>ofNullable(prop.getProperty("fc.solutionID")).orElseThrow(() -> new RuntimeMessageException(info, "Please specify the fc.solutionID in Tools->Options->User Properties Manager"));
/*     */         
/*  88 */         this.props = new FCEnrichProp(solutionID);
/*     */       } 
/*     */     }
/*     */     
/*  92 */     if (this.client == null) {
/*     */       
/*  94 */       String fsPath = prop.getProperty("fc.enrich.fs.path");
/*     */       
/*  96 */       if (fsPath != null) {
/*     */         
/*  98 */         this.client = (IDbEnrichClient)new FCLocalEnrich(new File(fsPath));
/*     */       }
/*     */       else {
/*     */         
/* 102 */         String dbURL = (String)Optional.<String>ofNullable(prop.getProperty("fc.enrich.db.url")).orElseThrow(() -> new RuntimeMessageException(info, "Please specify the fc.enrich.db.url in Tools->Options->User Properties Manager"));
/*     */         
/* 104 */         String driverName = (String)Optional.<String>ofNullable(prop.getProperty("fc.enrich.db.driver")).orElseThrow(() -> new RuntimeMessageException(info, "Please specify the fc.enrich.db.driver in Tools->Options->User Properties Manager"));
/*     */ 
/*     */         
/* 107 */         String userName = prop.getProperty("fc.enrich.db.user");
/* 108 */         String password = prop.getProperty("fc.enrich.db.password");
/* 109 */         SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(dbURL, driverName, userName, password);
/* 110 */         this.client = (IDbEnrichClient)new FCDBEnrich((DBPool)dbPool);
/*     */       } 
/*     */     } 
/*     */     
/* 114 */     if (this.client == null || this.props == null)
/*     */     {
/* 116 */       throw new RuntimeMessageException(info, "Failed to initialize");
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "saveDbDataExpire")
/*     */   public String saveDbDataExpire(IContivoRuntime icr, String dataItemNumber, String value, String duplicateOption, String daysToExpire, String key1, String key2, String key3, String key4) throws RuntimeMessageException {
/* 123 */     long startTime = System.currentTimeMillis();
/*     */     
/*     */     try {
/* 126 */       init(icr);
/* 127 */       int daysToExpireInt = parseInt(daysToExpire, "daysToExpire").intValue();
/* 128 */       if (daysToExpireInt < 0) {
/* 129 */         MessageInfo info = new MessageInfo("FCDBEnrich.saveDbData", new Tag[] { Tag.FATAL });
/* 130 */         throw new RuntimeMessageException(info, "Invalid value [" + daysToExpire + "] for daysToExpire. Value must be greater than 0");
/*     */       } 
/* 132 */       boolean ret = this.client.saveDbData(this.props.getSolutionID(), parseInt(dataItemNumber, "dataItemNumber").intValue(), key1, key2, key3, key4, value, parseInt(duplicateOption, "duplicateOption").intValue(), parseInt(daysToExpire, "daysToExpire").intValue());
/* 133 */       if (this.props.isTraceMode())
/*     */       {
/* 135 */         icr.addTraceMessage("FCEnrich.saveDbData", "{\"solutionId\":\"" + this.props
/* 136 */             .getSolutionID() + "\", \"dataItemNumber\":" + dataItemNumber + ", \"value\":\"" + value + "\", \"\":\"" + duplicateOption + "\", \"key1\":\"" + key1 + "\", \"key2\":\"" + key2 + "\", \"key3\":\"" + key3 + "\", \"key4\":\"" + key4 + "\"} = " + ret);
/*     */       }
/*     */ 
/*     */       
/* 140 */       return ret ? "true" : "false";
/*     */     }
/* 142 */     catch (RuntimeMessageException e) {
/*     */       
/* 144 */       throw e;
/*     */     }
/* 146 */     catch (Exception e) {
/*     */       
/* 148 */       MessageInfo info = new MessageInfo("FCEnrich.saveDbData", new Tag[] { Tag.FATAL });
/* 149 */       throw new RuntimeMessageException(e, info, e.getMessage());
/*     */     } finally {
/* 151 */       if (this.metrics != null) {
/* 152 */         this.metrics.incre("FCEnrich.saveDbData", 1L);
/* 153 */         this.metrics.incre("FCEnrich.saveDbDataTime", System.currentTimeMillis() - startTime);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "saveDbDataExpire")
/*     */   public String saveDbDataExpire(IContivoRuntime icr, String dataItemNumber, String value, String duplicateOption, String daysToExpire, String key1, String key2, String key3) throws RuntimeMessageException {
/* 162 */     return saveDbDataExpire(icr, dataItemNumber, value, duplicateOption, daysToExpire, key1, key2, key3, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "saveDbDataExpire")
/*     */   public String saveDbDataExpire(IContivoRuntime icr, String dataItemNumber, String value, String duplicateOption, String daysToExpire, String key1, String key2) throws RuntimeMessageException {
/* 169 */     return saveDbDataExpire(icr, dataItemNumber, value, duplicateOption, daysToExpire, key1, key2, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "saveDbDataExpire")
/*     */   public String saveDbDataExpire(IContivoRuntime icr, String dataItemNumber, String value, String duplicateOption, String daysToExpire, String key1) throws RuntimeMessageException {
/* 176 */     return saveDbDataExpire(icr, dataItemNumber, value, duplicateOption, daysToExpire, key1, null, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "saveDbData")
/*     */   public String saveDbData(IContivoRuntime icr, String dataItemNumber, String value, String duplicateOption, String key1, String key2, String key3, String key4) throws RuntimeMessageException {
/* 183 */     return saveDbDataExpire(icr, dataItemNumber, value, duplicateOption, "0", key1, key2, key3, key4);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "saveDbData")
/*     */   public String saveDbData(IContivoRuntime icr, String dataItemNumber, String value, String duplicateOption, String key1, String key2, String key3) throws RuntimeMessageException {
/* 190 */     return saveDbData(icr, dataItemNumber, value, duplicateOption, key1, key2, key3, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "saveDbData")
/*     */   public String saveDbData(IContivoRuntime icr, String dataItemNumber, String value, String duplicateOption, String key1, String key2) throws RuntimeMessageException {
/* 197 */     return saveDbData(icr, dataItemNumber, value, duplicateOption, key1, key2, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "saveDbData")
/*     */   public String saveDbData(IContivoRuntime icr, String dataItemNumber, String value, String duplicateOption, String key1) throws RuntimeMessageException {
/* 204 */     return saveDbData(icr, dataItemNumber, value, duplicateOption, key1, null, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbData")
/*     */   public String getDbData(IContivoRuntime icr, String dataItemNumber, String notFoundOption, String key1, String key2, String key3, String key4) throws RuntimeMessageException {
/* 211 */     long startTime = System.currentTimeMillis();
/*     */     
/*     */     try {
/* 214 */       init(icr);
/* 215 */       String ret = this.client.getDbData(this.props.getSolutionID(), parseInt(dataItemNumber, "dataItemNumber").intValue(), key1, key2, key3, key4, parseInt(notFoundOption, "notFoundOption").intValue());
/* 216 */       if (this.props.isTraceMode())
/*     */       {
/* 218 */         icr.addTraceMessage("FCEnrich.getDbData", "{\"solutionId\":\"" + this.props
/* 219 */             .getSolutionID() + "\", \"dataItemNumber\":" + dataItemNumber + ", \"key1\":\"" + key1 + "\", \"key2\":\"" + key2 + "\", \"key3\":\"" + key3 + "\", \"key4\":\"" + key4 + "\"} = " + ret);
/*     */       }
/*     */ 
/*     */       
/* 223 */       return (ret == null) ? "" : ret;
/*     */     }
/* 225 */     catch (RuntimeMessageException e) {
/*     */       
/* 227 */       throw e;
/*     */     }
/* 229 */     catch (Exception e) {
/*     */       
/* 231 */       MessageInfo info = new MessageInfo("FCEnrich.getDbData", new Tag[] { Tag.FATAL });
/* 232 */       throw new RuntimeMessageException(e, info, e.getMessage());
/*     */     } finally {
/* 234 */       if (this.metrics != null) {
/* 235 */         this.metrics.incre("FCEnrich.getDbData", 1L);
/* 236 */         this.metrics.incre("FCEnrich.getDbDataTime", System.currentTimeMillis() - startTime);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbData")
/*     */   public String getDbData(IContivoRuntime icr, String dataItemNumber, String notFoundOption, String key1, String key2, String key3) throws RuntimeMessageException {
/* 245 */     return getDbData(icr, dataItemNumber, notFoundOption, key1, key2, key3, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbData")
/*     */   public String getDbData(IContivoRuntime icr, String dataItemNumber, String notFoundOption, String key1, String key2) throws RuntimeMessageException {
/* 252 */     return getDbData(icr, dataItemNumber, notFoundOption, key1, key2, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   @ContivoExternalMethod(alias = "getDbData")
/*     */   public String getDbData(IContivoRuntime icr, String dataItemNumber, String notFoundOption, String key1) throws RuntimeMessageException {
/* 259 */     return getDbData(icr, dataItemNumber, notFoundOption, key1, null, null, null);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static Integer parseInt(String stringToParse, String name) throws RuntimeMessageException {
/*     */     try {
/* 266 */       return Integer.valueOf(Integer.parseInt(stringToParse));
/*     */     }
/* 268 */     catch (NumberFormatException e) {
/*     */       
/* 270 */       MessageInfo info = new MessageInfo("FCEnrich.parseInt", new Tag[] { Tag.FATAL });
/* 271 */       throw new RuntimeMessageException(info, name + " is not a integer");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\enrichment\FCEnrichCallCmd.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */