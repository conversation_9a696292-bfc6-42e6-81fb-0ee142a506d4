/*     */ package com.opentext.contivo.callcommand.fulcrum.seq.db;
/*     */ 
/*     */ import com.opentext.contivo.callcommand.fulcrum.db.RefNoApi;
/*     */ import java.sql.Connection;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FCDBSeqApi
/*     */   implements IFCDBSeqApi
/*     */ {
/*     */   protected static final String TBL_NAME = "EC_SEQUENCE_NUMBER";
/*     */   protected static final String TBL__REF_NO = "REF_NO";
/*     */   protected static final String TBL__SEQUENCE_KEY1 = "SEQUENCE_KEY1";
/*     */   protected static final String TBL__SEQUENCE_KEY2 = "SEQUENCE_KEY2";
/*     */   protected static final String TBL__SEQUENCE_KEY3 = "SEQUENCE_KEY3";
/*     */   protected static final String TBL__SEQUENCE_KEY4 = "SEQUENCE_KEY4";
/*     */   protected static final String TBL__LAST_SEQUENCE_NUMBER = "LAST_SEQUENCE_NUMBER";
/*     */   private static final String SEQNUM_REFNO_NAME = "seqnum_ref_no";
/*     */   
/*     */   public long getSequenceNumber(Connection conn, String key1, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/*  35 */     if (key1 == null)
/*     */     {
/*  37 */       throw new Exception("First key must not be null");
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  44 */     String sqlStmt = "SELECT LAST_SEQUENCE_NUMBER FROM  EC_SEQUENCE_NUMBER WHERE SEQUENCE_KEY1 = ? AND SEQUENCE_KEY2" + ((optionalKey1 != null) ? " = ? " : " IS NULL") + " AND SEQUENCE_KEY3" + ((optionalKey2 != null) ? " = ? " : " IS NULL") + " AND SEQUENCE_KEY4" + ((optionalKey3 != null) ? " = ? " : " IS NULL");
/*     */     
/*  46 */     PreparedStatement pStmt = null;
/*  47 */     ResultSet rset = null;
/*     */     
/*     */     try {
/*  50 */       pStmt = conn.prepareStatement(sqlStmt, 1004, 1007);
/*     */       
/*  52 */       int i = 1;
/*  53 */       pStmt.setString(i++, key1);
/*     */       
/*  55 */       if (optionalKey1 != null) {
/*  56 */         pStmt.setString(i++, optionalKey1);
/*     */       }
/*  58 */       if (optionalKey2 != null) {
/*  59 */         pStmt.setString(i++, optionalKey2);
/*     */       }
/*  61 */       if (optionalKey3 != null) {
/*  62 */         pStmt.setString(i++, optionalKey3);
/*     */       }
/*  64 */       pStmt.setFetchSize(1);
/*  65 */       rset = pStmt.executeQuery();
/*  66 */       if (rset.next())
/*     */       {
/*  68 */         return rset.getLong(1);
/*     */       }
/*     */     }
/*     */     finally {
/*     */       
/*  73 */       if (rset != null)
/*     */       {
/*  75 */         rset.close();
/*     */       }
/*  77 */       if (pStmt != null)
/*     */       {
/*  79 */         pStmt.close();
/*     */       }
/*     */     } 
/*  82 */     return 0L;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public long incrementSequenceNumberAndGet(Connection conn, long increment, String key1, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/*  88 */     boolean reset = false;
/*  89 */     if (conn.getAutoCommit()) {
/*  90 */       reset = true;
/*  91 */       conn.setAutoCommit(false);
/*     */     } 
/*     */     try {
/*  94 */       int incre = incrementSequenceNumber(conn, increment, key1, optionalKey1, optionalKey2, optionalKey3);
/*  95 */       long ret = (incre == 0) ? 0L : getSequenceNumber(conn, key1, optionalKey1, optionalKey2, optionalKey3);
/*  96 */       conn.commit();
/*  97 */       return ret;
/*  98 */     } catch (Throwable e) {
/*  99 */       conn.rollback();
/* 100 */       throw e;
/*     */     } finally {
/* 102 */       conn.commit();
/* 103 */       if (reset) {
/* 104 */         conn.setAutoCommit(true);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public int incrementSequenceNumber(Connection conn, long increment, String key1, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/* 112 */     if (key1 == null)
/*     */     {
/* 114 */       throw new Exception("First key must not be null");
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 121 */     String sqlStmt = "UPDATE EC_SEQUENCE_NUMBER SET  LAST_SEQUENCE_NUMBER = LAST_SEQUENCE_NUMBER + ? WHERE SEQUENCE_KEY1 = ?  AND SEQUENCE_KEY2" + ((optionalKey1 != null) ? " = ? " : " IS NULL") + " AND SEQUENCE_KEY3" + ((optionalKey2 != null) ? " = ? " : " IS NULL") + " AND SEQUENCE_KEY4" + ((optionalKey3 != null) ? " = ? " : " IS NULL");
/*     */     
/* 123 */     if (increment != 0L && increment != 1L) {
/* 124 */       throw new Exception("Increment value must be 0 or 1");
/*     */     }
/*     */     
/* 127 */     PreparedStatement pStmt = null;
/* 128 */     int result = 0;
/*     */     
/*     */     try {
/* 131 */       pStmt = conn.prepareStatement(sqlStmt, 1004, 1007);
/*     */       
/* 133 */       int i = 1;
/* 134 */       pStmt.setLong(i++, increment);
/* 135 */       pStmt.setString(i++, key1);
/*     */       
/* 137 */       if (optionalKey1 != null) {
/* 138 */         pStmt.setString(i++, optionalKey1);
/*     */       }
/* 140 */       if (optionalKey2 != null) {
/* 141 */         pStmt.setString(i++, optionalKey2);
/*     */       }
/* 143 */       if (optionalKey3 != null) {
/* 144 */         pStmt.setString(i++, optionalKey3);
/*     */       }
/* 146 */       result = pStmt.executeUpdate();
/*     */     }
/*     */     finally {
/*     */       
/* 150 */       if (pStmt != null)
/*     */       {
/* 152 */         pStmt.close();
/*     */       }
/*     */     } 
/* 155 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int updateSequenceNumber(Connection conn, long increment, String key1, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/* 162 */     if (key1 == null)
/*     */     {
/* 164 */       throw new Exception("First key must not be null");
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 171 */     String sqlStmt = "UPDATE EC_SEQUENCE_NUMBER SET  LAST_SEQUENCE_NUMBER = ? WHERE SEQUENCE_KEY1 = ?  AND SEQUENCE_KEY2" + ((optionalKey1 != null) ? " = ? " : " IS NULL") + " AND SEQUENCE_KEY3" + ((optionalKey2 != null) ? " = ? " : " IS NULL") + " AND SEQUENCE_KEY4" + ((optionalKey3 != null) ? " = ? " : " IS NULL");
/*     */     
/* 173 */     PreparedStatement pStmt = null;
/* 174 */     int result = 0;
/*     */     
/*     */     try {
/* 177 */       pStmt = conn.prepareStatement(sqlStmt, 1004, 1007);
/*     */       
/* 179 */       int i = 1;
/* 180 */       pStmt.setLong(i++, increment);
/* 181 */       pStmt.setString(i++, key1);
/*     */       
/* 183 */       if (optionalKey1 != null) {
/* 184 */         pStmt.setString(i++, optionalKey1);
/*     */       }
/* 186 */       if (optionalKey2 != null) {
/* 187 */         pStmt.setString(i++, optionalKey2);
/*     */       }
/* 189 */       if (optionalKey3 != null) {
/* 190 */         pStmt.setString(i++, optionalKey3);
/*     */       }
/* 192 */       result = pStmt.executeUpdate();
/*     */     }
/*     */     finally {
/*     */       
/* 196 */       if (pStmt != null)
/*     */       {
/* 198 */         pStmt.close();
/*     */       }
/*     */     } 
/* 201 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean insertSequenceNumber(Connection conn, long increment, String key1, String optionalKey1, String optionalKey2, String optionalKey3) throws Exception {
/* 207 */     if (key1 == null)
/*     */     {
/* 209 */       throw new Exception("First key must not be null");
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 221 */     String sqlStmt = "INSERT INTO EC_SEQUENCE_NUMBER ( SEQUENCE_KEY1, " + ((optionalKey1 != null) ? "SEQUENCE_KEY2, " : "") + ((optionalKey2 != null) ? "SEQUENCE_KEY3, " : "") + ((optionalKey3 != null) ? "SEQUENCE_KEY4, " : "") + "LAST_SEQUENCE_NUMBER, REF_NO ) SELECT ?, " + ((optionalKey1 != null) ? "?, " : "") + ((optionalKey2 != null) ? "?, " : "") + ((optionalKey3 != null) ? "?, " : "") + "?, ec_ref_no.\"VALUE\" FROM ec_ref_no WHERE ec_ref_no.name = 'seqnum_ref_no'";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 226 */     PreparedStatement pStmt = null;
/*     */     
/*     */     try {
/* 229 */       pStmt = conn.prepareStatement(sqlStmt, 1004, 1007);
/*     */       
/* 231 */       int i = 1;
/* 232 */       pStmt.setString(i++, key1);
/*     */       
/* 234 */       if (optionalKey1 != null) {
/* 235 */         pStmt.setString(i++, optionalKey1);
/*     */       }
/* 237 */       if (optionalKey2 != null) {
/* 238 */         pStmt.setString(i++, optionalKey2);
/*     */       }
/* 240 */       if (optionalKey3 != null) {
/* 241 */         pStmt.setString(i++, optionalKey3);
/*     */       }
/* 243 */       pStmt.setLong(i++, increment);
/* 244 */       return pStmt.execute();
/*     */     }
/*     */     finally {
/*     */       
/* 248 */       if (pStmt != null)
/*     */       {
/* 250 */         pStmt.close();
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public int updateRefNo(Connection conn) throws SQLException {
/* 257 */     return RefNoApi.updateRefNo(conn, "seqnum_ref_no");
/*     */   }
/*     */ 
/*     */   
/*     */   public void insertRefNo(Connection conn) throws SQLException {
/* 262 */     RefNoApi.insertRefNo(conn, "seqnum_ref_no");
/*     */   }
/*     */ 
/*     */   
/*     */   public long selectRefNo(Connection conn) throws Exception {
/* 267 */     return RefNoApi.selectRefNo(conn, "seqnum_ref_no");
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\seq\db\FCDBSeqApi.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */