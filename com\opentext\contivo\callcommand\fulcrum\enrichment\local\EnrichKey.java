/*    */ package com.opentext.contivo.callcommand.fulcrum.enrichment.local;
/*    */ 
/*    */ import com.google.common.base.Objects;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class EnrichKey
/*    */ {
/*    */   private final String key1;
/*    */   private final String key2;
/*    */   private final String key3;
/*    */   private final String key4;
/*    */   private final String dataItemNumber;
/*    */   private final int hashCode;
/*    */   
/*    */   public EnrichKey(String key1, String key2, String key3, String key4, String dataItemNumber) {
/* 29 */     this.key1 = key1;
/* 30 */     this.key2 = key2;
/* 31 */     this.key3 = key3;
/* 32 */     this.key4 = key4;
/* 33 */     this.dataItemNumber = dataItemNumber;
/* 34 */     int hash = key1.hashCode();
/* 35 */     hash ^= (key2 != null) ? key2.hashCode() : 0;
/* 36 */     hash ^= (key3 != null) ? key3.hashCode() : 0;
/* 37 */     hash ^= (key4 != null) ? key4.hashCode() : 0;
/* 38 */     hash ^= dataItemNumber.hashCode();
/* 39 */     this.hashCode = hash;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public boolean equals(Object other) {
/* 45 */     if (this == other)
/* 46 */       return true; 
/* 47 */     if (other == null)
/* 48 */       return false; 
/* 49 */     if (!(other instanceof EnrichKey)) {
/* 50 */       return false;
/*    */     }
/* 52 */     EnrichKey otherKey = (EnrichKey)other;
/* 53 */     boolean sameKey1 = Objects.equal(this.key1, otherKey.key1);
/* 54 */     boolean sameKey2 = Objects.equal(this.key2, otherKey.key2);
/* 55 */     boolean sameKey3 = Objects.equal(this.key3, otherKey.key3);
/* 56 */     boolean sameKey4 = Objects.equal(this.key4, otherKey.key4);
/* 57 */     boolean sameIndex = Objects.equal(this.dataItemNumber, otherKey.dataItemNumber);
/* 58 */     return (sameKey1 && sameKey2 && sameKey3 && sameKey4 && sameIndex);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public int hashCode() {
/* 65 */     return this.hashCode;
/*    */   }
/*    */ 
/*    */   
/*    */   public String toString() {
/* 70 */     return this.key1 + "," + this.key1 + ((this.key2 != null) ? (this.key2 + ",") : ",") + ((this.key3 != null) ? (this.key3 + ",") : ",") + ((this.key4 != null) ? (this.key4 + ",") : ",");
/*    */   }
/*    */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\enrichment\local\EnrichKey.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */