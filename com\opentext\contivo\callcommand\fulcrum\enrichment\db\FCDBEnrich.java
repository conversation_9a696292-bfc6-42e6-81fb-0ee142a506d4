/*     */ package com.opentext.contivo.callcommand.fulcrum.enrichment.db;
/*     */ 
/*     */ import com.contivo.mixedruntime.RuntimeMessageException;
/*     */ import com.contivo.transform.MessageInfo;
/*     */ import com.contivo.transform.Tag;
/*     */ import com.opentext.contivo.callcommand.fulcrum.enrichment.IDbEnrichClient;
/*     */ import com.opentext.contivo.callcommand.fulcrum.xref.db.DBPool;
/*     */ import java.sql.Connection;
/*     */ import java.sql.SQLException;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FCDBEnrich
/*     */   implements IDbEnrichClient
/*     */ {
/*     */   private final DBPool connectionPool;
/*     */   private final int retries;
/*     */   
/*     */   public FCDBEnrich(DBPool connectionPool) {
/*  30 */     this.connectionPool = connectionPool;
/*  31 */     this.retries = 3;
/*     */   }
/*     */ 
/*     */   
/*     */   public FCDBEnrich(DBPool connectionPool, int retries) {
/*  36 */     this.connectionPool = connectionPool;
/*  37 */     this.retries = retries;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean saveDbData(String solutionId, int dataItemNumber, String key1, String key2, String key3, String key4, String value, int duplicateOption, int daysToExpire) throws Exception {
/*  44 */     for (int i = 0; i < this.retries; i++) {
/*  45 */       Connection conn = this.connectionPool.getConnection(); 
/*  46 */       try { boolean reset = false;
/*  47 */         if (conn.getAutoCommit()) {
/*  48 */           reset = true;
/*  49 */           conn.setAutoCommit(false);
/*     */         } 
/*     */         
/*  52 */         try { int ret = FCDBEnrichApi.updateDbData(conn, solutionId, dataItemNumber, daysToExpire, key1, key2, key3, key4, value);
/*     */           
/*  54 */           if (ret != 0) {
/*  55 */             if (duplicateOption == 1)
/*  56 */             { conn.commit(); }
/*  57 */             else if (duplicateOption == 2)
/*     */             
/*  59 */             { conn.rollback(); }
/*  60 */             else { if (duplicateOption == 3) {
/*  61 */                 conn.rollback();
/*  62 */                 throw new Exception("Duplicate record found!");
/*     */               } 
/*  64 */               conn.rollback();
/*  65 */               throw new Exception("Invalid duplicateOption: " + duplicateOption); }
/*     */           
/*     */           } else {
/*  68 */             if (FCDBEnrichApi.updateRefNo(conn) == 0) {
/*  69 */               FCDBEnrichApi.insertRefNo(conn);
/*     */             }
/*     */             
/*  72 */             FCDBEnrichApi.saveDbData(conn, solutionId, dataItemNumber, daysToExpire, key1, key2, key3, key4, value);
/*  73 */             conn.commit();
/*     */           } 
/*  75 */           boolean bool = true;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/*  86 */           if (reset)
/*  87 */             conn.setAutoCommit(true);  return bool; } catch (SQLException e) { if (!conn.getAutoCommit()) conn.rollback();  } catch (Throwable e) { if (!conn.getAutoCommit()) conn.rollback();  throw e; } finally { if (reset) conn.setAutoCommit(true);
/*     */            }
/*     */ 
/*     */         
/*  91 */         if (conn != null) conn.close();  } catch (Throwable throwable) { if (conn != null)
/*     */           try { conn.close(); } catch (Throwable throwable1) { throwable.addSuppressed(throwable1); }   throw throwable; } 
/*  93 */     }  MessageInfo info = new MessageInfo("FCDBEnrich.saveDbData", new Tag[] { Tag.FATAL });
/*  94 */     throw new RuntimeMessageException(info, "Exceeded max retries");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDbData(String solutionId, int dataItemNumber, String key1, String key2, String key3, String key4, int resultFlag) throws Exception {
/* 101 */     Connection conn = this.connectionPool.getConnection();
/*     */     try {
/* 103 */       String ret = FCDBEnrichApi.getDbData(conn, solutionId, dataItemNumber, key1, key2, key3, key4);
/* 104 */       if (ret == null && resultFlag != 1) {
/*     */         
/* 106 */         if (resultFlag == 2)
/*     */         {
/* 108 */           throw new Exception("Record not Found!");
/*     */         }
/*     */ 
/*     */         
/* 112 */         throw new Exception("Invalid resultFlag: " + resultFlag);
/*     */       } 
/*     */ 
/*     */       
/* 116 */       String str1 = ret;
/* 117 */       if (conn != null) conn.close(); 
/*     */       return str1;
/*     */     } catch (Throwable throwable) {
/*     */       if (conn != null)
/*     */         try {
/*     */           conn.close();
/*     */         } catch (Throwable throwable1) {
/*     */           throwable.addSuppressed(throwable1);
/*     */         }  
/*     */       throw throwable;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Development\contivo\Contivo_Analyst_24.4.1\callcommands\contivo-fulcrum-callcommands-2.4.1.1.jar!\com\opentext\contivo\callcommand\fulcrum\enrichment\db\FCDBEnrich.class
 * Java compiler version: 11 (55.0)
 * JD-Core Version:       1.1.3
 */