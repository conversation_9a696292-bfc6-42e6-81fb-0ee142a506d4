package com.opentext.contivo.callcommand.fulcrum.xref.migration;

import com.opentext.contivo.callcommand.fulcrum.xref.*;
import com.opentext.contivo.callcommand.fulcrum.xref.db.*;
import com.opentext.contivo.callcommand.fulcrum.xref.filesystem.FileSystemLookUp;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.io.TempDir;

import java.io.*;
import java.nio.file.Path;
import java.sql.*;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test suite to validate identical behavior between file-based and database-based lookups
 */
public class FCXRefMigrationTest {
    
    // Test configuration
    private static final String TEST_SOLUTION_ID = "TEST_SOLUTION";
    private static final String TEST_TABLE_NAME = "wo1_CONTIVO";
    private static final String DB_URL = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1";
    private static final String DB_DRIVER = "org.h2.Driver";
    
    // Test data - matches your DELL.COM.01_wo1_CONTIVO 1.txt file
    private static final String TEST_FILE_CONTENT = 
        "\"wo1_CONTIVO\",\"\"\n" +
        "\"A\",\"Apple\"\n" +
        "\"MES-EMFC\",\"EMFC\"\n" +
        "\"MES-AMFF\",\"AMFF\"\n" +
        "\"COMPAL\",\"D\"\n";
    
    @TempDir
    Path tempDir;
    
    private Connection testConnection;
    private File testFile;
    private FileSystemLookUp fileSystemLookup;
    private FCLookUp databaseLookup;
    
    @BeforeEach
    void setUp() throws Exception {
        // Setup H2 in-memory database
        setupDatabase();
        
        // Create test file
        setupTestFile();
        
        // Initialize lookup clients
        fileSystemLookup = new FileSystemLookUp(tempDir.toFile());
        
        SimpleConnectionToDBPool dbPool = new SimpleConnectionToDBPool(DB_URL, DB_DRIVER, null, null);
        databaseLookup = new FCLookUp(dbPool);
        
        // Migrate file to database
        migrateTestData();
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (testConnection != null && !testConnection.isClosed()) {
            testConnection.close();
        }
    }
    
    private void setupDatabase() throws Exception {
        Class.forName(DB_DRIVER);
        testConnection = DriverManager.getConnection(DB_URL);
        
        // Create database schema
        String[] createStatements = {
            "CREATE SEQUENCE TGTS_LOOKUP_SEQ START WITH 1000",
            "CREATE SEQUENCE TGTS_LOOKUP_ROW_SEQ START WITH 2000",
            
            "CREATE TABLE TGTS_LOOKUP (" +
            "  TABLE_ID BIGINT PRIMARY KEY," +
            "  SOLUTION_ID VARCHAR(255) NOT NULL," +
            "  TABLE_NAME VARCHAR(255) NOT NULL," +
            "  CREATE_DATE TIMESTAMP NOT NULL," +
            "  AVAILABLE_DATE TIMESTAMP" +
            ")",
            
            "CREATE TABLE TGTS_LOOKUP_ROW (" +
            "  ROW_ID BIGINT PRIMARY KEY," +
            "  FK_TABLE_ID BIGINT NOT NULL," +
            "  ROW_TYPE INT NOT NULL," +
            "  FOREIGN KEY (FK_TABLE_ID) REFERENCES TGTS_LOOKUP(TABLE_ID)" +
            ")",
            
            "CREATE TABLE TGTS_LOOKUP_COLUMN (" +
            "  FK_ROW_ID BIGINT NOT NULL," +
            "  COL_NUM INT NOT NULL," +
            "  COL_VAL VARCHAR(4000)," +
            "  PRIMARY KEY (FK_ROW_ID, COL_NUM)," +
            "  FOREIGN KEY (FK_ROW_ID) REFERENCES TGTS_LOOKUP_ROW(ROW_ID)" +
            ")"
        };
        
        try (Statement stmt = testConnection.createStatement()) {
            for (String sql : createStatements) {
                stmt.execute(sql);
            }
        }
    }
    
    private void setupTestFile() throws Exception {
        // Create file with exact naming convention expected by FileSystemLookUp
        // Format: {solutionID}_{tableName}_hdr.txt (for files with headers)
        testFile = tempDir.resolve(TEST_SOLUTION_ID + "_" + TEST_TABLE_NAME + "_hdr.txt").toFile();
        
        try (FileWriter writer = new FileWriter(testFile)) {
            writer.write(TEST_FILE_CONTENT);
        }
    }
    
    private void migrateTestData() throws Exception {
        FileToDatabaseMigrationUtility migrator = new FileToDatabaseMigrationUtility(
            DB_URL, DB_DRIVER, null, null);
        
        FileToDatabaseMigrationUtility.MigrationResult result = migrator.migrateFileToDatabase(
            testFile.getAbsolutePath(), TEST_SOLUTION_ID, TEST_TABLE_NAME, true);
        
        System.out.println("Migration result: " + result);
    }
    
    @Test
    @DisplayName("Test identical lookup results - single column by number")
    void testSingleColumnLookupByNumber() throws Exception {
        // Test data: Looking for "A" in column 1, expecting "Apple" from column 2
        String[] findCols = {"1"};
        String resultCol = "2";
        String[] findValues = {"A"};
        Op[] ops = {Op.EQ};
        
        // File-based lookup
        String fileResult = fileSystemLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        
        // Database-based lookup
        String dbResult = databaseLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        
        // Results should be identical
        assertEquals(fileResult, dbResult, "File and database lookups should return identical results");
        assertEquals("Apple", fileResult, "Expected lookup result should be 'Apple'");
    }
    
    @Test
    @DisplayName("Test identical lookup results - single column by name")
    void testSingleColumnLookupByName() throws Exception {
        // Test data: Looking for "MES-EMFC" in column "wo1_CONTIVO", expecting "EMFC" from column ""
        String[] findCols = {"wo1_CONTIVO"};
        String resultCol = "";  // Second column header is empty string
        String[] findValues = {"MES-EMFC"};
        Op[] ops = {Op.EQ};
        
        // File-based lookup
        String fileResult = fileSystemLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, true);
        
        // Database-based lookup
        String dbResult = databaseLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, true);
        
        // Results should be identical
        assertEquals(fileResult, dbResult, "File and database lookups should return identical results");
        assertEquals("EMFC", fileResult, "Expected lookup result should be 'EMFC'");
    }
    
    @Test
    @DisplayName("Test identical lookup results - not found case")
    void testLookupNotFound() throws Exception {
        String[] findCols = {"1"};
        String resultCol = "2";
        String[] findValues = {"NONEXISTENT"};
        Op[] ops = {Op.EQ};
        
        // File-based lookup
        String fileResult = fileSystemLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        
        // Database-based lookup
        String dbResult = databaseLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        
        // Both should return null for not found
        assertEquals(fileResult, dbResult, "File and database lookups should return identical results");
        assertNull(fileResult, "Expected null for not found lookup");
    }
    
    @Test
    @DisplayName("Test identical lookup results - case insensitive operation")
    void testCaseInsensitiveLookup() throws Exception {
        String[] findCols = {"1"};
        String resultCol = "2";
        String[] findValues = {"a"};  // lowercase 'a' instead of 'A'
        Op[] ops = {Op.CI};  // Case insensitive
        
        // File-based lookup
        String fileResult = fileSystemLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        
        // Database-based lookup
        String dbResult = databaseLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        
        // Results should be identical
        assertEquals(fileResult, dbResult, "File and database lookups should return identical results");
        assertEquals("Apple", fileResult, "Case insensitive lookup should find 'Apple'");
    }
    
    @Test
    @DisplayName("Test identical lookup results - not equal operation")
    void testNotEqualOperation() throws Exception {
        // This test verifies that NOT_EQ operation works identically
        // Note: This will return the first non-matching result
        String[] findCols = {"1"};
        String resultCol = "2";
        String[] findValues = {"A"};
        Op[] ops = {Op.NOT_EQ};
        
        // File-based lookup
        String fileResult = fileSystemLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        
        // Database-based lookup
        String dbResult = databaseLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        
        // Results should be identical
        assertEquals(fileResult, dbResult, "File and database lookups should return identical results");
        assertNotNull(fileResult, "NOT_EQ operation should find a result");
        assertNotEquals("Apple", fileResult, "NOT_EQ should not return 'Apple'");
    }
    
    @Test
    @DisplayName("Test identical lookup results - all test data")
    void testAllDataLookups() throws Exception {
        // Test all rows in the test data
        String[][] testCases = {
            {"A", "Apple"},
            {"MES-EMFC", "EMFC"},
            {"MES-AMFF", "AMFF"},
            {"COMPAL", "D"}
        };
        
        for (String[] testCase : testCases) {
            String findValue = testCase[0];
            String expectedResult = testCase[1];
            
            String[] findCols = {"1"};
            String resultCol = "2";
            String[] findValues = {findValue};
            Op[] ops = {Op.EQ};
            
            // File-based lookup
            String fileResult = fileSystemLookup.lookupValue(
                TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
            
            // Database-based lookup
            String dbResult = databaseLookup.lookupValue(
                TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
            
            // Verify identical results
            assertEquals(fileResult, dbResult, 
                String.format("File and database lookups should return identical results for '%s'", findValue));
            assertEquals(expectedResult, fileResult, 
                String.format("Expected result for '%s' should be '%s'", findValue, expectedResult));
        }
    }
    
    @Test
    @DisplayName("Test header mapping consistency")
    void testHeaderMapping() throws Exception {
        // Verify that header mappings are identical between file and database
        
        // This test requires access to internal Table structure
        // We'll test by attempting column name lookups and verifying they work identically
        
        String[] findCols = {"wo1_CONTIVO"};  // First column header
        String resultCol = "";                 // Second column header (empty string)
        String[] findValues = {"A"};
        Op[] ops = {Op.EQ};
        
        // File-based lookup by column name
        String fileResult = fileSystemLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, true);
        
        // Database-based lookup by column name
        String dbResult = databaseLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, true);
        
        // Results should be identical
        assertEquals(fileResult, dbResult, "Header-based lookups should return identical results");
        assertEquals("Apple", fileResult, "Header-based lookup should find 'Apple'");
    }
    
    @Test
    @DisplayName("Performance comparison test")
    void testPerformanceComparison() throws Exception {
        int iterations = 1000;
        String[] findCols = {"1"};
        String resultCol = "2";
        String[] findValues = {"A"};
        Op[] ops = {Op.EQ};

        // Warm up
        for (int i = 0; i < 100; i++) {
            fileSystemLookup.lookupValue(TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
            databaseLookup.lookupValue(TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        }

        // File-based performance test
        long fileStartTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            fileSystemLookup.lookupValue(TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        }
        long fileEndTime = System.nanoTime();
        long fileTime = fileEndTime - fileStartTime;

        // Database-based performance test
        long dbStartTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            databaseLookup.lookupValue(TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);
        }
        long dbEndTime = System.nanoTime();
        long dbTime = dbEndTime - dbStartTime;

        System.out.printf("Performance comparison (%d iterations):\n", iterations);
        System.out.printf("File-based: %.2f ms (%.2f μs per lookup)\n",
                         fileTime / 1_000_000.0, fileTime / (iterations * 1000.0));
        System.out.printf("Database-based: %.2f ms (%.2f μs per lookup)\n",
                         dbTime / 1_000_000.0, dbTime / (iterations * 1000.0));
        System.out.printf("Ratio: %.2fx\n", (double) dbTime / fileTime);

        // Performance test passes if both complete successfully
        assertTrue(fileTime > 0, "File-based lookups should complete");
        assertTrue(dbTime > 0, "Database-based lookups should complete");
    }

    @Test
    @DisplayName("Test edge cases - empty values and special characters")
    void testEdgeCases() throws Exception {
        // Test lookup with empty string (second column header)
        String[] findCols = {"2"};
        String resultCol = "1";
        String[] findValues = {""};  // Empty string value
        Op[] ops = {Op.EQ};

        // File-based lookup
        String fileResult = fileSystemLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);

        // Database-based lookup
        String dbResult = databaseLookup.lookupValue(
            TEST_SOLUTION_ID, TEST_TABLE_NAME, findCols, resultCol, findValues, ops, false);

        // Results should be identical
        assertEquals(fileResult, dbResult, "Empty value lookups should return identical results");
    }
}
